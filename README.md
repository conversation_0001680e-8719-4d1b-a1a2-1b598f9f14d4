# Galaxy Creator

![Galaxy Creator Logo](docs/images/logo.png)

Galaxy Creator 是一个基于AI的多模态内容创作平台，支持文本、语音和视频的智能生成，帮助创作者高效生产优质内容。


## 功能特点

### 多模态内容创作
- **文本创作**：基于大语言模型的智能文本生成
- **语音合成**：将文本转换为自然流畅的语音
- **视频合成**：结合音频和视频素材进行智能合成

### 角色系统
- 创建和管理多个"演员"角色
- 为不同角色配置专属技能和特性
- 基于角色特性生成个性化内容

### 热门话题
- 提供实时热门话题推荐
- 按日期查询热门话题列表
- 基于热门话题快速生成相关内容

### 媒体资源管理
- 上传和管理个人媒体素材
- 支持多种媒体类型（图片、视频、音频）
- 素材智能分类和检索

## 技术架构

### 后端技术
- **框架**：Spring Boot3.x + Spring webflux
- **编程模式**：响应式编程（Reactor）
- **项目结构**：多模块Maven项目
- **数据存储**：MySQL + Redis
- **文件存储**：阿里云OSS
- **认证方式**：JWT + 手机验证码

### AI模型集成
- 支持多种大语言模型
- 文本、图像、音视频模型集成
- 可配置的模型参数和API密钥

## 快速开始

### 环境要求
- JDK 21
- Maven 3.6+
- MySQL 5.7+
- Redis 6.0+
- 阿里云OSS账号（用于媒体存储）


## 开发指南

### 项目结构
```
galaxycreator/ # 项目根目录
├─ api # API相关模块目录
│  ├─ gxcadmin # 管理后台API模块
│  │  ├─ java # Java源代码目录
│  │  │  └─ com
│  │  │      └─ lazhu
│  │  │          └─ business # 业务逻辑代码目录
│  │  │              ├─ actor # 演员相关业务模块
│  │  │              ├─ actorconfig # 演员配置相关业务模块
│  │  │              ├─ content # 内容相关业务模块
│  │  │              ├─ hottopics # 热门话题相关业务模块
│  │  │              ├─ llmconfig # 大语言模型配置相关业务模块
│  │  │              ├─ llmvoicejoin # 大语言模型语音关联相关业务模块
│  │  │              ├─ mediaassetsimg # 图片媒体资源相关业务模块
│  │  │              ├─ mediaassetsvideo # 视频媒体资源相关业务模块
│  │  │              ├─ mediaassetsvoice # 音频媒体资源相关业务模块
│  │  │              ├─ moduleitems # 模块项相关业务模块
│  │  │              ├─ user # 用户相关业务模块
│  │  │              └─ usermediaassets # 用户媒体资源相关业务模块
│  │  └─ resources # 资源文件目录
│  │      └─ config # 配置文件目录
│  └─ gxcapi # 对外API模块
│      ├─ java # Java源代码目录
│      │  └─ com
│      │      └─ lazhu
│      │          ├─ common # 公共代码目录
│      │          ├─ config # 配置代码目录
│      │          ├─ filter # 过滤器代码目录
│      │          ├─ llm # 大语言模型相关代码目录
│      │          ├─ support # 支持类代码目录
│      │          ├─ utils # 工具类代码目录
│      │          └─ web # Web层代码目录
│      └─ resources # 资源文件目录
│          └─ config # 配置文件目录
├─ base # 基础模块目录
│  ├─ baseai # 基础AI模块
│  │  └─ java # Java源代码目录
│  │      └─ baseai
│  │          └─ tool
│  │              └─ audio # 音频相关工具代码目录
│  └─ basebus # 基础业务模块
│      ├─ java # Java源代码目录
│      │  └─ com
│      │      └─ lazhu
│      │          ├─ business # 业务逻辑代码目录
│      │          │  ├─ actor # 演员相关业务模块
│      │          │  ├─ actorconfig # 演员配置相关业务模块
│      │          │  ├─ content # 内容相关业务模块
│      │          │  ├─ hottopics # 热门话题相关业务模块
│      │          │  ├─ llmconfig # 大语言模型配置相关业务模块
│      │          │  ├─ llmvoicejoin # 大语言模型语音关联相关业务模块
│      │          │  ├─ mediaassetsimg # 图片媒体资源相关业务模块
│      │          │  ├─ mediaassetsvideo # 视频媒体资源相关业务模块
│      │          │  ├─ mediaassetsvoice # 音频媒体资源相关业务模块
│      │          │  ├─ moduleitems # 模块项相关业务模块
│      │          │  ├─ user # 用户相关业务模块
│      │          │  └─ usermediaassets # 用户媒体资源相关业务模块
│      │          ├─ common # 公共代码目录
│      │          ├─ exception # 异常处理代码目录
│      │          └─ system # 系统相关代码目录
│      └─ resources # 资源文件目录
└─ doc # 文档目录

```

### 开发环境设置

1. **IDE配置**
   - 推荐使用IntelliJ IDEA
   - 安装Lombok插件
   - 启用Annotation Processing

2. **代码风格**
   - 使用项目提供的代码格式化模板
   - 遵循阿里巴巴Java开发规范
   - 确保代码通过Checkstyle检查

3. **本地开发环境**
   ```bash
   # 安装必要的开发工具
   # Windows
   choco install openjdk21 maven redis mysql

   # 配置MySQL
   mysql -u root -p
   CREATE DATABASE galaxy_creator CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
   
   # 配置Redis
   redis-cli
   config set requirepass your_password
   ```

4. **启动开发服务器**
   ```bash
   # 启动Redis
   redis-server

   # 启动应用（开发模式）
   mvn spring-boot:run -Dspring.profiles.active=dev
   ```

### 开发流程
1. 下载代码
2. 创建功能分支
3. 提交代码变更
4. 创建Pull Request

### 代码提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式化
- refactor: 代码重构
- test: 测试相关
- chore: 构建/工具链相关

## 快速演示

以下是使用Galaxy Creator创建内容的基本流程：

1. **登录系统**：使用手机号和验证码登录
2. **选择角色**：从角色库中选择一个合适的角色
3. **选择话题**：从热门话题中选择或自定义话题
4. **生成内容**：点击"创建内容"按钮，系统将基于角色和话题生成内容
5. **编辑优化**：根据需要编辑和优化生成的内容
6. **导出分享**：将内容导出为不同格式或直接分享到社交媒体

## API文档
``
https://6pvs50j8pv.apifox.cn/
密码：2ILHkLs4
``

## 路线图

以下是我们计划在未来版本中添加的功能：

- [ ] 增强视频编辑功能
- [ ] 添加内容模板库
- [ ] 开发移动端应用
- [ ] 支持saas版本

### 认证与授权
- 基于JWT的身份验证
- 手机号验证码双重认证
- 细粒度的权限控制

### 数据安全
- 敏感数据加密存储
- HTTPS安全传输
- 定期数据备份

### AI内容安全
- 内容审核机制
- 版权保护措施
- 隐私数据过滤



## 常见问题 (FAQ)

### Q: 如何添加新的AI模型？
A: 在系统中添加新的AI模型需要在LlmConfig实体中配置相应的模型信息，包括模型类型、提供商和API密钥。详细步骤请参考开发文档。

### Q: 系统支持哪些类型的媒体文件？
A: 目前系统支持上传和处理的媒体类型包括：图片(jpg, png, gif)、音频(mp3, wav)和视频(mp4, mov)文件。

### Q: 如何创建自定义角色？
A: 登录系统后，导航至"角色管理"页面，点击"创建新角色"按钮，填写角色信息并设置角色特性。

## 更新日志

### v1.0.0 (2025-07-01)
- 初始版本发布
- 支持基本的文本、语音和视频生成功能
- 实现角色系统和媒体资源管理


## 项目依赖

Galaxy Creator项目依赖：

- [Spring Boot](https://spring.io/projects/spring-boot) - Spring-Boot 应用框架
- [Spring webflux](https://docs.spring.io/spring-framework/reference/web/webflux.html) Spring-Webflux
- [Project Reactor](https://projectreactor.io/) - 响应式编程库
- [MyBatis-Plus](https://baomidou.com/) - ORM框架
- [Redisson](https://redisson.org/) - Redis客户端
- [Lombok](https://projectlombok.org/) - Java代码简化工具

