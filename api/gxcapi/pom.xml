<project
	xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.lazhu</groupId>
		<artifactId>galaxycreator</artifactId>
		<version>1.0.0</version>
		<relativePath>../../pom.xml</relativePath>
	</parent>
	<artifactId>gxcapi</artifactId>
	<packaging>war</packaging>
	<dependencies>
	<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-tomcat</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-freemarker</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
		<!-- 打包WEB -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-tomcat</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.tomcat.embed</groupId>
			<artifactId>tomcat-embed-jasper</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.lazhu</groupId>
			<artifactId>torch-sim</artifactId>
			<version>${torch.version}</version>
		</dependency>
		<dependency>
			<groupId>com.lazhu</groupId>
			<artifactId>torch-upload</artifactId>
			<version>${torch.version}</version>
		</dependency>
		<dependency>
			<groupId>com.lazhu</groupId>
			<artifactId>basebus</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>slf4j-simple</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!--汉字转拼音-->
		<dependency>
			<groupId>io.github.biezhi</groupId>
			<artifactId>TinyPinyin</artifactId>
			<version>2.0.3.RELEASE</version>
		</dependency>
        <dependency>
            <groupId>com.lazhu</groupId>
            <artifactId>gxc-publish</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
        
        <!-- MongoDB相关依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb-reactive</artifactId>
        </dependency>


    </dependencies>
	<build>
		<resources>
			<resource>
				<directory>src/main/java</directory>
				<filtering>false</filtering>
				<includes>
					<include>**/xml/*.xml</include>
				</includes>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>false</filtering>
				<includes>
					<include>**.*</include>
					<include>**/*.*</include>
					<include>**/*/*.*</include>
				</includes>
				<excludes>
					<exclude>application-dev.yml</exclude>
					<exclude>application-test.yml</exclude>
					<exclude>application-prepare.yml</exclude>
					<exclude>application-released.yml</exclude>
					<exclude>config/redisson-dev.yml</exclude>
					<exclude>config/redisson-test.yml</exclude>
					<exclude>config/redisson-prepare.yml</exclude>
					<exclude>config/redisson-released.yml</exclude>
				</excludes>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
				<includes>
					<include>application-${profiles.active}.yml</include>
					<include>config/redisson-${profiles.active}.yml</include>
				</includes>
			</resource>
		</resources>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>
</project>