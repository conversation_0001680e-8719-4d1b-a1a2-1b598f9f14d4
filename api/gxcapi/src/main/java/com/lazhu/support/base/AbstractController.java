package com.lazhu.support.base;

import com.lazhu.common.exception.BusinessException;
import com.lazhu.common.exception.PermissionException;
import com.lazhu.common.exception.ValidationException;
import com.lazhu.support.response.SimpleResponse;
import com.lazhu.support.response.SimpleResponseBuilder;
import com.lazhu.utils.WebTool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;

public abstract class AbstractController {

	protected final Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public SimpleResponse<String> handleBusinessException(BusinessException e) {
        String path = WebTool.getRequestURI();
        logger.error("业务异常 [{}]: {}", path, e.getMessage(),e);

        return new SimpleResponseBuilder<>("").setHttpCode(e.getCode()).setMsg("业务异常: " + e.getMessage()).bulider();
    }

    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(ValidationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public SimpleResponse<String> handleValidationException(ValidationException e) {
        String path = WebTool.getRequestURI();
        String field = e.getField() != null ? e.getField() : "未知字段";
        logger.error("参数验证失败 [{}]: {} - {}", path, field, e.getMessage(),e);

        return new SimpleResponseBuilder<String>("").error("参数验证失败 [" + field + "]: " + e.getMessage()).bulider();
    }

    /**
     * 处理权限异常
     */
    @ExceptionHandler(PermissionException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public SimpleResponse<String> handlePermissionException(PermissionException e) {
        String path = WebTool.getRequestURI();
        String resource = e.getResource() != null ? e.getResource() : "未知资源";
        String action = e.getAction() != null ? e.getAction() : "未知操作";

        logger.error("权限不足 [{}]: {} - {} - {}", path, resource, action, e.getMessage());

        return new SimpleResponseBuilder<String>("").error("权限不足: " + e.getMessage()).bulider();
    }

    /**
     * 处理所有其他未捕获的异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public SimpleResponse<String> handleException(Exception e) {
        logger.error("未处理的异常 [{}]: {}", WebTool.getRequestURI(), e.getMessage(), e);

        // 默认返回SimpleResponse
        return new SimpleResponseBuilder<String>("").error("系统错误: " + e.getMessage()).bulider();
    }
}
