package com.lazhu.filter;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * token参数配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "spring.token")
public class TokenProp {

    /**
     * token 密钥
     */
    private String secret;

    /**
     * token 过期时间 单位秒
     */
    private Long expireTime;

    /**
     * 允许访问的url
     */
    private List<String> accessUrl;


}
