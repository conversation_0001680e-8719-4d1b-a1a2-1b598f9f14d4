package com.lazhu.filter;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.lazhu.common.Const;
import com.lazhu.common.RedisKeyConst;
import com.lazhu.support.config.SpringContext;
import com.lazhu.support.response.SimpleResponse;
import com.lazhu.support.response.SimpleResponseBuilder;
import com.lazhu.support.util.JwtTokenUtil;
import jakarta.servlet.*;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.util.AntPathMatcher;

import java.io.IOException;
import java.io.PrintWriter;

/**
 * 登录拦截器
 */
@Slf4j
@WebFilter(filterName = "loginFilter", urlPatterns = "/api/*", asyncSupported = true)
public class LoginFilter implements Filter {


    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        TokenProp tp = SpringContext.getBean(TokenProp.class);
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        String requestURI = request.getRequestURI();
        AntPathMatcher pathMatcher = new AntPathMatcher();
        boolean isAccess = tp.getAccessUrl().stream().anyMatch(url -> pathMatcher.match(url, requestURI));
        if (isAccess) {
            filterChain.doFilter(servletRequest, servletResponse);
        } else {
            String token = request.getHeader(Const.SESSION_TOKEN);
            if (StrUtil.isBlank(token)) {
                writeResponse(servletResponse, 10000, "请登录！");
                return;
            }
            boolean vt = JwtTokenUtil.vaildToken(token, tp.getSecret());
            if (!vt) {
                writeResponse(servletResponse, 10000, "请登录！");
                return;
            }
            // 判断token是否无效
            RedissonClient redissonClient = SpringContext.getBean(RedissonClient.class);
            RSet<Object> set = redissonClient.getSet(RedisKeyConst.INVALID_TOKEN);
            if (set.isExists() && set.contains(token)) {
                writeResponse(servletResponse, 10000, "请登录！");
                return;
            }
            filterChain.doFilter(servletRequest, servletResponse);
        }

    }

    private void writeResponse(ServletResponse response, Integer code, String error) throws IOException {
        response.setContentType("application/json;charset=utf-8");
        PrintWriter writer = response.getWriter();
        SimpleResponse<String> res = new SimpleResponseBuilder<String>().setHttpCode(code).setMsg(error).bulider();
        writer.append(JSON.toJSONString(res));
        writer.flush();
        writer.close();
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        Filter.super.init(filterConfig);
    }

    @Override
    public void destroy() {
        Filter.super.destroy();
    }
}
