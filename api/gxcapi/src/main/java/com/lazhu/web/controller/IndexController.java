package com.lazhu.web.controller;

import cn.hutool.core.bean.BeanUtil;
import com.lazhu.business.moduleitems.entity.ModuleItems;
import com.lazhu.business.moduleitems.entity.ModuleItemsQuery;
import com.lazhu.business.moduleitems.service.ModuleItemsService;
import com.lazhu.support.base.AbstractController;
import com.lazhu.support.response.ArrayResponse;
import com.lazhu.support.response.ArrayResponseBuilder;
import com.lazhu.support.response.SimpleResponse;
import com.lazhu.support.response.SimpleResponseBuilder;
import com.lazhu.system.dic.entity.Dic;
import com.lazhu.system.dic.entity.DicQuery;
import com.lazhu.system.dic.entity.DicVO;
import com.lazhu.system.dic.service.DicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 首页接口
 */
@RequestMapping("/api")
@RestController
public class IndexController extends AbstractController {


    @Autowired
    private DicService dicService;

    @Autowired
    private ModuleItemsService moduleItemsService;


    /**
     * 获取字典列表
     */
    @GetMapping(value = "/dict/list")
    public Mono<SimpleResponse<HashMap<String, List<DicVO>>>> queryDict(DicQuery param) {
        return Mono.create(sike -> {
            param.setAscs("order_num");
            List<Dic> list = dicService.queryList(BeanUtil.beanToMap(param));
            //根据code分组
            Map<String, List<DicVO>> group = list.stream()
                    .map(e -> {
                        DicVO vo = new DicVO(e.getCode(), e.getValue(), e.getTxt());
                        vo.setOrderNum(e.getOrderNum());
                        return vo;
                    })
                    .collect(Collectors.groupingBy(DicVO::getCode));
            sike.success(new SimpleResponseBuilder<>(new HashMap<>(group)).success().bulider());
        });
    }

    /**
     * 获取模块列表
     */
    @GetMapping(value = "/module/list")
    public Mono<ArrayResponse<ModuleItems>> query(ModuleItemsQuery param) {
        return Mono.create(sike -> {
            List<ModuleItems> list = moduleItemsService.queryList(BeanUtil.beanToMap(param));
            sike.success(new ArrayResponseBuilder<>(list).success().bulider());
        });
    }


}
