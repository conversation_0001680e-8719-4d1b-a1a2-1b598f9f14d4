package com.lazhu.web.controller;

import cn.hutool.core.bean.BeanUtil;
import com.lazhu.business.actorconfig.entity.ActorConfig;
import com.lazhu.business.actorconfig.entity.ActorConfigQuery;
import com.lazhu.business.actorconfig.service.ActorConfigService;
import com.lazhu.support.base.AbstractController;
import com.lazhu.support.response.SimpleResponse;
import com.lazhu.support.response.SimpleResponseBuilder;
import com.lazhu.utils.WebTool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 人物配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@RestController
@RequestMapping(value = "api/ac")
public class ActorConfigController extends AbstractController {

    @Autowired
    private ActorConfigService actorConfigService;

    @PostMapping(value = "/read/list")
    public SimpleResponse<HashMap<String, List<ActorConfig>>> query(@RequestBody ActorConfigQuery param) {
        param.setUserId(WebTool.getUserId());
        List<ActorConfig> ac = actorConfigService.queryList(BeanUtil.beanToMap(param));
        //根据type分组
        Map<String, List<ActorConfig>> group = ac.stream().collect(Collectors.groupingBy(ActorConfig::getCharacterType));
        return new SimpleResponseBuilder<>(new HashMap<>(group)).success().bulider();
    }
}
