package com.lazhu.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.lazhu.business.mediaassets.entity.UserMediaAssets;
import com.lazhu.support.base.AbstractController;
import com.lazhu.support.response.PageResponse;
import com.lazhu.support.response.PageResponseBuilder;
import com.lazhu.support.response.SimpleResponse;
import com.lazhu.support.response.SimpleResponseBuilder;
import com.lazhu.utils.WebTool;
import com.lazhu.web.dto.MediaAssertsDTO;
import com.lazhu.web.dto.MediaAssertsQuery;
import com.lazhu.web.service.MediaAssertsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

/**
 * 素材接口
 */
@RestController
@RequestMapping(value = "/api/asserts")
public class MediaAssertsController extends AbstractController {

    @Autowired
    private MediaAssertsService mediaAssetsService;

    /**
     * 素材列表
     */
    @PostMapping(value = "/list")
    public Mono<PageResponse<MediaAssertsDTO>> list(@RequestBody MediaAssertsQuery query) {
        return Mono.create(sike -> {
            query.setUserId(WebTool.getUserId());
            Page<MediaAssertsDTO> list = mediaAssetsService.queryAssertsList(query);
            sike.success(new PageResponseBuilder<>(list).success().builder());
        });
    }

    /**
     * 上传素材
     */
    @PostMapping(value = "/upload")
    public Mono<SimpleResponse<UserMediaAssets>> upload(@RequestBody UserMediaAssets param) {
        return Mono.create(sike -> {
            param.setCreateBy(WebTool.getUserId());
            UserMediaAssets assets = mediaAssetsService.saveUserAsserts(param);
            sike.success(new SimpleResponseBuilder<>(assets).success().bulider());
        });
    }

    /**
     * 删除素材
     */
    @GetMapping(value = "/del")
    public Mono<SimpleResponse<String>> del(@RequestParam Long id) {
        return Mono.create(sike -> {
            mediaAssetsService.deleteById(id);
            sike.success(new SimpleResponseBuilder<String>().success().bulider());
        });
    }


}
