package com.lazhu.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.lazhu.common.RedisKeyConst;
import com.lazhu.baseai.llm.combine.VideoCombineResult;
import com.lazhu.support.base.AbstractController;
import com.lazhu.support.response.SimpleResponse;
import com.lazhu.support.response.SimpleResponseBuilder;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 第三方服务接口
 */
@Slf4j
@RestController
@RequestMapping("/api/third")
public class ThirdAPIController extends AbstractController {


    @Autowired
    private RedissonClient redissonClient;

    /**
     * 视频合成回调接口
     */
    @PostMapping("/video/synthesize/callback")
    public SimpleResponse<String> videoSynthesizeCallback(@RequestBody JSONObject param) {
        log.info("接受到视频合成回调, param:{}", param);
        //获取任务id
        String taskId = param.getString("task_id");
        RMap<String, String> map = redissonClient.getMap(RedisKeyConst.VIDEO_SYNTHESIZE_TASK);
        VideoCombineResult videoCombineResult = new VideoCombineResult();
        videoCombineResult.setTaskId(taskId);
        videoCombineResult.setDone(true);
        if (param.getInteger("code") != 200) {
            log.error("{} 视频合成失败", taskId);
            videoCombineResult.setResult("0");
            videoCombineResult.setErrorMsg(param.getString("msg") + " ==> " + param.getString("data"));
        } else {
            log.info("{} 视频合成成功", taskId);
            videoCombineResult.setResult("1");
            JSONObject data = param.getJSONObject("data");
            videoCombineResult.setCoverImage(data.getString("cover_url"));
            videoCombineResult.setVideoPath(data.getString("video_url"));
        }
        map.put(taskId, JSONObject.toJSONString(videoCombineResult));
        return new SimpleResponseBuilder<>("").success().bulider();
    }

}
