package com.lazhu.web.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.lazhu.business.user.entity.User;
import com.lazhu.common.utils.KmsSecretUtil;
import com.lazhu.support.util.StringUtils;
import lombok.Data;

import java.io.Serializable;

/**
 * 登录相关参数
 */
public class LoginParam {

    /**
     * 登录请求参数
     *
     * @param mobile     手机号
     * @param verifyCode 短信验证码
     */
    public record LoginReq(String mobile, String verifyCode) {
    }

    /**
     * 登录响应参数
     */
    @Data
    public static class LoginResp implements Serializable {

        @JSONField(serializeUsing = ToStringSerializer.class)
        private Long userId;
        private String nickName;
        private String mobile;
        private String headImg;

        public static LoginResp of(User user) {
            LoginResp resp = new LoginResp();
            resp.setUserId(user.getId());
            resp.setNickName(user.getNickName());
            resp.setMobile(StringUtils.fuzzMobile(KmsSecretUtil.decrypt(user.getMobile())));
            resp.setHeadImg(user.getHeadImg());
            return resp;
        }
    }

}
