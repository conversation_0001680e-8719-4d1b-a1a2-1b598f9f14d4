package com.lazhu.web.dto;

import com.lazhu.support.base.BaseQuery;
import lombok.Data;

import java.util.List;

/**
 * 素材查询参数
 */
@Data
public class MediaAssertsQuery extends BaseQuery {

    /**
     * 素材来源 1 系统 2用户素材
     */
    private Integer source;


    private Long userId;

    /**
     * 演员id
     */
    private Long actorId;


    /**
     * 演员名称
     */
    private String actorName;

    /**
     * 演员id
     */
    private List<Long> actorIds;

    /**
     * 素材类型
     */
    private String assetsType;

    /**
     * 标题
     */
    private String title;

}
