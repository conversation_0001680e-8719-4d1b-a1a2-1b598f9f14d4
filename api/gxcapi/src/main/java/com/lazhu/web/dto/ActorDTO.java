package com.lazhu.web.dto;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.lazhu.business.actor.entity.Actor;
import com.lazhu.business.actor.entity.ActorPrompt;
import com.lazhu.business.actorcontentexample.entity.ActorContentExample;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ActorDTO implements Serializable {

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    //nick_name 昵称
    private String nickName;
    //head_img 头像
    private String headImg;
    //profile 简介
    private String profile;
    //role_prompt 人设提示词
    private String rolePrompt;
    //视频素材数量
    private Integer videoMaterialCount;
    //音频素材数量
    private Integer audioMaterialCount;
    // 示例内容
    private ActorContentExample exampleContent;


    /**
     * 根据actor 构建 ActorDTO
     */
    public static ActorDTO buildFromActor(Actor actor) {
        ActorDTO actorDTO = new ActorDTO();
        BeanUtil.copyProperties(actor, actorDTO);
        return actorDTO;
    }

}
