package com.lazhu.web.controller;

import com.lazhu.business.content.entity.Content;
import com.lazhu.business.content.service.ContentService;
import com.lazhu.gxc.publish.service.ContentPublishService;
import com.lazhu.gxc.publish.service.UserChannelAccountService;
import com.lazhu.support.base.AbstractController;
import com.lazhu.support.response.SimpleResponse;
import com.lazhu.support.response.SimpleResponseBuilder;
import com.lazhu.utils.WebTool;
import com.lazhu.web.dto.PublishRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * 发布接口
 */
@RestController
@RequestMapping(value = "/api/pub")
public class PublishController extends AbstractController {

    @Autowired
    private ContentPublishService publishService;

    @Autowired
    private UserChannelAccountService userChannelAccountService;

    @Autowired
    private ContentService contentService;

    /**
     * 发布
     */
    @PostMapping("/publish")
    public Mono<SimpleResponse<String>> publish(@RequestBody PublishRequest request) {
        Long userId = WebTool.getUserId();
        // 获取账号
//        List<Integer> channels = request.getChannels();
//        for (Integer channel : channels) {
//            ChannelType channelType = ChannelType.fromType(channel);
//            UserChannelAccount account = userChannelAccountService.queryUserChannelAccount(userId, channelType, null);
//            if (account == null) {
//                throw new BusinessException(StrUtil.format("请先绑定 {} 平台账号", channelType.getDesc()));
//            }
//            publishService.publish(request.getContentId(), channelType, account.getCookie());
//        }
        Content content = new Content();
        content.setId(request.getContentId());
        content.setStatus(1);
        contentService.update(content);
        return Mono.fromCallable(() -> new SimpleResponseBuilder<String>().success().bulider());
    }
}
