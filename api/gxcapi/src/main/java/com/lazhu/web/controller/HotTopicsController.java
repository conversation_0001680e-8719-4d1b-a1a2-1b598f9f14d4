package com.lazhu.web.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.mongo.entity.HotNews;
import com.lazhu.mongo.service.HotNewsService;
import com.lazhu.support.base.AbstractController;
import com.lazhu.support.response.ArrayResponse;
import com.lazhu.support.response.ArrayResponseBuilder;
import com.lazhu.support.response.PageResponse;
import com.lazhu.support.response.PageResponseBuilder;
import com.lazhu.web.dto.HotTopicsDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 热点话题api
 */
@RestController
@RequestMapping("/api/ht")
public class HotTopicsController extends AbstractController {

    @Autowired
    private HotNewsService hotNewsService;


    /**
     * 获取所有数据来源
     *
     * @return 数据来源列表
     */
    @GetMapping("/sources")
    public Mono<ArrayResponse<String>> getAllSources() {
        return hotNewsService.findAllSources()
                .collectList()
                .map(source -> new ArrayResponseBuilder<>(source).success().bulider());
    }


    /**
     * 获取热点话题（从MongoDB查询）
     *
     * @param page 页码
     * @param size 每页大小
     * @param source 数据来源（可选）
     * @param topicDate 热点日期（可选，格式：yyyy-MM-dd）
     * @param keyword 关键字（可选）
     * @return 列表
     */
    @GetMapping("/list")
    public Mono<PageResponse<HotTopicsDTO>> hotTopicsList(
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "size", defaultValue = "20") int size,
            @RequestParam(value = "platSource", required = false) String source,
            @RequestParam(value = "topicDate", required = false) String topicDate,
            @RequestParam(value = "keyword", required = false) String keyword) {

        Flux<HotNews> hotNews;
        Mono<Long> total;

        // 根据参数组合选择不同的查询方法
        boolean hasSource = StrUtil.isNotBlank(source);
        boolean hasDate = StrUtil.isNotBlank(topicDate);
        boolean hasKeyword = StrUtil.isNotBlank(keyword);

        if (hasSource && hasDate && hasKeyword) {
            // 来源 + 日期 + 关键字
            hotNews = hotNewsService.findHotNewsBySourceAndDateAndKeyword(source, topicDate, keyword, page-1, size);
            total = hotNewsService.countHotNewsBySourceAndDateAndKeyword(source, topicDate, keyword);
        } else if (hasSource && hasDate) {
            // 来源 + 日期
            hotNews = hotNewsService.findHotNewsBySourceAndDate(source, topicDate, page-1, size);
            total = hotNewsService.countHotNewsBySourceAndDate(source, topicDate);
        } else if (hasSource && hasKeyword) {
            // 来源 + 关键字
            hotNews = hotNewsService.findHotNewsBySourceAndKeyword(source, keyword, page-1, size);
            total = hotNewsService.countHotNewsBySourceAndKeyword(source, keyword);
        } else if (hasDate && hasKeyword) {
            // 日期 + 关键字
            hotNews = hotNewsService.findHotNewsByDateAndKeyword(topicDate, keyword, page-1, size);
            total = hotNewsService.countHotNewsByDateAndKeyword(topicDate, keyword);
        } else if (hasSource) {
            // 仅来源
            hotNews = hotNewsService.findHotNewsBySource(source, page-1, size);
            total = hotNewsService.countHotNewsBySource(source);
        } else if (hasDate) {
            // 仅日期
            hotNews = hotNewsService.findHotNewsByDate(topicDate, page-1, size);
            total = hotNewsService.countHotNewsByDate(topicDate);
        } else if (hasKeyword) {
            // 仅关键字
            hotNews = hotNewsService.findHotNewsByKeyword(keyword, page-1, size);
            total = hotNewsService.countHotNewsByKeyword(keyword);
        } else {
            // 无条件查询
            hotNews = hotNewsService.findHotNewsList(page-1, size);
            total = hotNewsService.countHotNews();
        }

        return hotNews.map(HotTopicsDTO::convertToHotTopics)
                .collectList()
                .zipWith(total)
                .map(tuple -> {
                    Page<HotTopicsDTO> p = new Page<>(page, size, tuple.getT2());
                    p.setRecords(tuple.getT1());
                    return new PageResponseBuilder<>(p).success().builder();
                });
    }

    /**
     * 搜索热点话题（支持按关键词和来源搜索）
     *
     * @param keyword 关键词（必填）
     * @param source 来源（可选）
     * @return 列表
     */
    @GetMapping("/search")
    public Mono<ArrayResponse<HotTopicsDTO>> searchHotNews(
            @RequestParam("keyword") String keyword,
            @RequestParam(value = "source", required = false) String source) {

        Flux<HotNews> searchResult;

        if (StrUtil.isNotBlank(source)) {
            // 按来源和关键词搜索
            searchResult = hotNewsService.searchNewsBySourceAndKeyword(source, keyword);
        } else {
            // 仅按关键词搜索（搜索多个字段）
            searchResult = hotNewsService.searchNewsByTopic(keyword);
        }

        return searchResult
                .map(HotTopicsDTO::convertToHotTopics)
                .collectList()
                .map(data -> new ArrayResponseBuilder<>(data).success().bulider());
    }


}
