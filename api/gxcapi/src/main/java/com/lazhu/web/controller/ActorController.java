package com.lazhu.web.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.lazhu.business.actor.entity.Actor;
import com.lazhu.business.actor.entity.ActorQuery;
import com.lazhu.business.actor.service.ActorService;
import com.lazhu.business.actorcontentexample.entity.ActorContentExample;
import com.lazhu.business.actorcontentexample.service.ActorContentExampleService;
import com.lazhu.common.enums.MediaAssetTypeEnum;
import com.lazhu.baseai.tool.txt.PromptUtil;
import com.lazhu.common.exception.BusinessException;
import com.lazhu.common.exception.PermissionException;
import com.lazhu.support.base.AbstractController;
import com.lazhu.support.response.ArrayResponse;
import com.lazhu.support.response.ArrayResponseBuilder;
import com.lazhu.support.response.SimpleResponse;
import com.lazhu.support.response.SimpleResponseBuilder;
import com.lazhu.utils.WebTool;
import com.lazhu.web.dto.ActorDTO;
import com.lazhu.web.dto.MediaAssetsCount;
import com.lazhu.web.service.MediaAssertsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 演员接口
 */
@RestController
@RequestMapping(value = "/api/actor")
public class ActorController extends AbstractController {

    @Autowired
    private ActorService actorService;

    @Autowired
    private MediaAssertsService mediaAssertsService;

    @Autowired
    private ActorContentExampleService actorContentExampleService;

    /**
     * 演员列表
     */
    @GetMapping(value = "/list")
    public Mono<ArrayResponse<ActorDTO>> list(ActorQuery query) {
        // 设置查询条件
        Long userId = WebTool.getUserId();
        query.setCreateBy(userId);
        query.setUseStatus(1);
        return Mono.fromCallable(() -> {
            // 查询数据
            return actorService.queryList(BeanUtil.beanToMap(query));
        }).map(list -> {
            //获取演员id
            List<Long> actorIds = list.stream().map(Actor::getId).toList();
            //根据演员id查询素材数量
            List<MediaAssetsCount> mediaAssetsCounts = mediaAssertsService.getMediaAssetsCount(actorIds);
            //根据actorId和类型分组
            Map<Long, Map<String, List<MediaAssetsCount>>> group = mediaAssetsCounts.stream().collect(Collectors.groupingBy(MediaAssetsCount::getActorId, Collectors.groupingBy(MediaAssetsCount::getType)));
            //数据封装
            List<ActorDTO> actorDTOList = new ArrayList<>();
            for (Actor actor : list) {
                ActorDTO actorDTO = ActorDTO.buildFromActor(actor);
                Map<String, List<MediaAssetsCount>> am = group.get(actor.getId());
                if (CollectionUtil.isNotEmpty(am)) {
                    List<MediaAssetsCount> videoCount = am.get(MediaAssetTypeEnum.VIDEO.getType());
                    if (CollectionUtil.isNotEmpty(videoCount)) {
                        actorDTO.setVideoMaterialCount(videoCount.getFirst().getCount());
                    }
                    List<MediaAssetsCount> audioCount = am.get(MediaAssetTypeEnum.AUDIO.getType());
                    if (CollectionUtil.isNotEmpty(audioCount)) {
                        actorDTO.setAudioMaterialCount(audioCount.getFirst().getCount());
                    }
                }
                actorDTOList.add(actorDTO);
            }
            // 构建响应
            return new ArrayResponseBuilder<>(actorDTOList).success().bulider();
        }).subscribeOn(Schedulers.boundedElastic());
    }

    /**
     * 演员详情
     */
    @GetMapping(value = "/detail")
    public Mono<SimpleResponse<ActorDTO>> detail(@RequestParam Long id) {
        return Mono.fromCallable(() -> {
            // 查询数据
            return actorService.queryById(id);
        }).map(actor -> {
            // 构建响应
            return new SimpleResponseBuilder<>(ActorDTO.buildFromActor(actor)).success().bulider();
        }).subscribeOn(Schedulers.boundedElastic());
    }


    /**
     * 创建演员
     */
    @PostMapping(value = "/create")
    public Mono<SimpleResponse<String>> create(@RequestBody ActorDTO param) {
        Long userId = WebTool.getUserId();
        Actor actor = BeanUtil.toBean(param, Actor.class);
        actor.setRolePrompt(param.getRolePrompt());
        return Mono.fromCallable(() -> {
            if (actor.getId() == null) {
                actor.setCreateBy(userId);
                actorService.save(actor);
            } else {
                // 检查是否是自己创建的演员
                Actor existingActor = actorService.queryById(actor.getId());
                if (existingActor == null) {
                    throw new BusinessException("演员不存在");
                }

                // 权限检查
                assert userId != null;
                if (!userId.equals(existingActor.getCreateBy())) {
                    throw new PermissionException("actor", "update", "无权修改其他用户的演员");
                }
                actorService.update(actor);
            }
            ActorContentExample exampleContent = param.getExampleContent();
            if(exampleContent != null){
                exampleContent.setActorId(actor.getId());
                actorContentExampleService.saveOrUpdate(exampleContent);
            }
            return new SimpleResponseBuilder<>(actor.getId().toString()).success().bulider();
        }).subscribeOn(Schedulers.boundedElastic());
    }

    /**
     * 删除角色
     */
    @GetMapping(value = "/del")
    public Mono<SimpleResponse<String>> del(@RequestParam Long id) {
        Long userId = WebTool.getUserId();
        return Mono.fromCallable(() -> {
            Actor actor = actorService.queryById(id);
            if (actor == null) {
                throw new BusinessException("演员不存在");
            }
            // 权限检查
            assert userId != null;
            if (!userId.equals(actor.getCreateBy())) {
                throw new PermissionException("actor", "delete", "无权删除");
            }
            // 删除角色 （逻辑删除）
            actor.setUseStatus(0);
            actorService.update(actor);
            return new SimpleResponseBuilder<>("").success().bulider();
        }).subscribeOn(Schedulers.boundedElastic());
    }


    @Autowired
    private PromptUtil promptUtil;

    /**
     * 生成提示词
     */
    @PostMapping("/createPrompt")
    public SimpleResponse<String> createPrompt(@RequestBody JSONObject param) {
        String profile = param.getString("profile");
        String prompt = promptUtil.createPrompt(profile);
        return new SimpleResponseBuilder<>(prompt).success().bulider();
    }
}
