package com.lazhu.web.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.gxc.publish.common.ChannelType;
import com.lazhu.gxc.publish.entity.UserChannelAccount;
import com.lazhu.gxc.publish.entity.UserChannelAccountDTO;
import com.lazhu.gxc.publish.entity.UserChannelAccountQuery;
import com.lazhu.gxc.publish.service.UserChannelAccountService;
import com.lazhu.support.base.AbstractController;
import com.lazhu.support.response.PageResponse;
import com.lazhu.support.response.PageResponseBuilder;
import com.lazhu.support.response.SimpleResponse;
import com.lazhu.support.response.SimpleResponseBuilder;
import com.lazhu.utils.WebTool;
import com.lazhu.web.dto.UserChannelAccountLoginReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

/**
 * 用户账户管理
 */
@RestController
@RequestMapping("/api/uc")
public class UserChannelController extends AbstractController {

    @Autowired
    private UserChannelAccountService userChannelAccountService;

    /**
     * 新增账号
     */
    @PostMapping("/add")
    public Mono<SimpleResponse<String>> add(@RequestBody UserChannelAccountLoginReq req) {
        Long userId = WebTool.getUserId();
        return Mono.fromCallable(() -> {
            ChannelType channelTypeEnum = ChannelType.fromType(req.getChannel());
            if (channelTypeEnum == null) {
                throw new RuntimeException("无效的渠道");
            }
            userChannelAccountService.login(userId, channelTypeEnum, req.getAccount());
            return new SimpleResponseBuilder<>("").success().bulider();
        });
    }


    /**
     * 删除账号
     */
    @GetMapping("/del")
    public Mono<SimpleResponse<String>> del(@RequestParam Long id) {
        return Mono.fromCallable(() -> {
            userChannelAccountService.removeById(id);
            return new SimpleResponseBuilder<>("").success().bulider();
        });
    }

    /**
     * 账号列表
     */
    @PostMapping("/list")
    public Mono<PageResponse<UserChannelAccountDTO>> list(@RequestBody UserChannelAccountQuery param) {
        param.setUserId(WebTool.getUserId());
        return Mono.fromCallable(() -> {
            Page<UserChannelAccountDTO> page = userChannelAccountService.queryPage(param);
            return new PageResponseBuilder<>(page).success().builder();
        });
    }

    /**
     * 账号登录状态同步
     */
    @PostMapping("/syncLoginStatus")
    public Mono<PageResponse<UserChannelAccountDTO>> sync(@RequestBody UserChannelAccountQuery param) {
        param.setUserId(WebTool.getUserId());
        return Mono.fromCallable(() -> userChannelAccountService.queryPage(param)).map(page -> {
            for (UserChannelAccountDTO item : page.getRecords()) {
                // 如果是在线状态则 自动同步最新登录状态
                if (ObjectUtil.equal(item.getLoginStatus(), 1)) {
                    ChannelType channelType = ChannelType.fromType(item.getChannel());
                    boolean login = userChannelAccountService.checkLogin(item.getCookie(), channelType);
                    item.setLoginStatus(login ? 1 : 0);
                    //更新登录状态
                    UserChannelAccount account = new UserChannelAccount();
                    account.setId(item.getId());
                    account.setLoginStatus(item.getLoginStatus());
                    userChannelAccountService.updateById(account);
                }
            }
            return new PageResponseBuilder<>(page).success().builder();
        });
    }
}
