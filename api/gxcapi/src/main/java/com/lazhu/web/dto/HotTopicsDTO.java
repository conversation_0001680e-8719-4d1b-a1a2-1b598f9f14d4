package com.lazhu.web.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.lazhu.mongo.entity.HotNews;
import lombok.Data;

import java.io.Serializable;

@Data
public class HotTopicsDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    //plat_source 来源平台
    private String platSource;
    //icon 小图标;枚举：飙升 置顶  热门
    private String icon;
    //topic_title 标题
    private String topicTitle;
    //topic_date 榜单日期;yyyy-MM-dd
    private String topicDate;
    //rank 热度排名
    private String rank;
    //tag 标签
    private String tag;
    // url 链接
    private String url;

    public static HotTopicsDTO convertToHotTopics(HotNews hotNews) {
        HotTopicsDTO hotTopics = new HotTopicsDTO();
        hotTopics.setPlatSource(hotNews.getSource());
        hotTopics.setTopicTitle(hotNews.getTopic());
        hotTopics.setTopicDate(hotNews.getDate());
        hotTopics.setTag(hotNews.getKind());
        hotTopics.setRank(hotNews.getHeat());
        hotTopics.setUrl(hotNews.getUrl());
        return hotTopics;
    }
    
}
