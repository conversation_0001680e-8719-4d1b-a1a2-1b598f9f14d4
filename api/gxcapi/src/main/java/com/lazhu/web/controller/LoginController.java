package com.lazhu.web.controller;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.lazhu.business.user.entity.User;
import com.lazhu.business.user.service.UserService;
import com.lazhu.common.Const;
import com.lazhu.common.LzSmsConst;
import com.lazhu.common.RedisKeyConst;
import com.lazhu.common.exception.BusinessException;
import com.lazhu.filter.TokenProp;
import com.lazhu.support.base.AbstractController;
import com.lazhu.support.base.BaseToken;
import com.lazhu.support.config.SpringContext;
import com.lazhu.support.response.SimpleResponse;
import com.lazhu.support.response.SimpleResponseBuilder;
import com.lazhu.support.util.JwtTokenUtil;
import com.lazhu.utils.IpUtil;
import com.lazhu.utils.LzAliSmsTool;
import com.lazhu.web.dto.LoginParam;
import jakarta.servlet.http.HttpServletRequest;
import org.redisson.api.RBucket;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;

import static com.lazhu.common.Const.SMS_MAX_SEND_TIMES;

/**
 * 登录
 */
@RequestMapping("/api")
@RestController
public class LoginController extends AbstractController {

    @Autowired
    private UserService userService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private LzAliSmsTool lzAliSmsTool;

    @Autowired
    private TokenProp tokenProp;

    /**
     * 登录
     */
    @PostMapping("/login")
    public SimpleResponse<LoginParam.LoginResp> login(@RequestBody LoginParam.LoginReq param) {
        //校验验证码是否正确
        RBucket<Object> bucket = redissonClient.getBucket(RedisKeyConst.SMS_CODE + param.mobile());
        if (!bucket.isExists() || ObjectUtil.notEqual(param.verifyCode(), bucket.get())) {
            return new SimpleResponseBuilder<LoginParam.LoginResp>().error().setMsg("验证码错误").bulider();
        }
        //验证成功清除验证码
        bucket.delete();
        User user = userService.queryByMobile(param.mobile());
        if (user != null && ObjectUtil.equal(user.getUserStatus(), "0")) {
            //账户被禁用
            return new SimpleResponseBuilder<LoginParam.LoginResp>().error().setMsg("账户异常").bulider();
        }
        if (user == null) {
            //用户不存在，创建用户
//            user = userService.createUser(param.mobile());
            throw new BusinessException("账号不存在");
        }
        //生成token
        BaseToken bt = new BaseToken();
        bt.setUserId(user.getId().toString());
        bt.setFirstTime(System.currentTimeMillis());
        bt.setExpireTime(System.currentTimeMillis() + tokenProp.getExpireTime() * 1000);
        String token = JwtTokenUtil.createToken(bt, tokenProp.getSecret());
        LoginParam.LoginResp resp = LoginParam.LoginResp.of(user);
        return new SimpleResponseBuilder<>(resp).success().token(token).bulider();
    }

    /**
     * 发送短信验证码
     */
    @GetMapping("/sendSms")
    public SimpleResponse<String> sendSms(HttpServletRequest request, String mobile) {
        //判断环境
        String sysEnvScience = SpringContext.getSysEnvScience();
        String code;
        if (!"released".equalsIgnoreCase(sysEnvScience)) {
            //非正式环境
            code = Const.DEV_SMS_CODE;
        } else {
            //正式环境
            //验证发送次数是否超过限制
            RBucket<Integer> bucket = redissonClient.getBucket(RedisKeyConst.SMS_SEND_COUNT + mobile);
            if (bucket.isExists() && bucket.get() != null && bucket.get() > SMS_MAX_SEND_TIMES) {
                return new SimpleResponseBuilder<String>().error().setMsg("发送次数超过限制").bulider();
            }
            String ip = IpUtil.getIpAddr(request);
            RBucket<Integer> ipBucket = redissonClient.getBucket(RedisKeyConst.SMS_SEND_COUNT_IP + ip);
            if (ipBucket.isExists() && ipBucket.get() != null && ipBucket.get() > SMS_MAX_SEND_TIMES) {
                return new SimpleResponseBuilder<String>().error().setMsg("发送次数超过限制").bulider();
            }
            //生成6位随机数
            code = RandomUtil.randomNumbers(6);
            //发送短信
            lzAliSmsTool.sendCodeSms(LzSmsConst.lzkj, mobile, code);
            //保存发送次数
            bucket.set(bucket.isExists() ? Convert.toInt(bucket.get(), 0) + 1 : 1, Duration.ofDays(1));
            ipBucket.set(ipBucket.isExists() ? Convert.toInt(ipBucket.get(), 0) : 1, Duration.ofDays(1));
        }
        //存入缓存 (5分钟有效)
        redissonClient.getBucket(RedisKeyConst.SMS_CODE + mobile).set(code, Duration.ofMinutes(5));
        return new SimpleResponseBuilder<>("").success().bulider();
    }

    /**
     * 退出登录
     */
    @GetMapping("/logout")
    public SimpleResponse<String> logout(@RequestHeader("token") String token) {
        // 解析token获取用户信息
        BaseToken baseToken = JwtTokenUtil.getToken(token, tokenProp.getSecret(), BaseToken.class);
        if (baseToken == null) {
            return new SimpleResponseBuilder<>("").success().bulider();
        }
        long expireTime = baseToken.getExpireTime() - System.currentTimeMillis();
        if(expireTime > 0){
            // 通过将token加入缓存实现过期效果
            RSet<Object> set = redissonClient.getSet(RedisKeyConst.INVALID_TOKEN);
            set.add(token);
            set.expire(Duration.ofMillis(expireTime));
        }
        return new SimpleResponseBuilder<>("").success().bulider();
    }


}
