package com.lazhu.web.service;

import cn.hutool.core.bean.BeanUtil;
import com.lazhu.baseai.llm.dto.LLMBaseResponse;
import com.lazhu.baseai.llm.dto.VideoSynthesizeTask;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSONObject;
import com.lazhu.business.content.dto.ContentCreationReq;
import com.lazhu.business.llmconfig.service.LLMService;
import com.lazhu.business.tasks.entity.Tasks;
import com.lazhu.business.tasks.entity.TasksQuery;
import com.lazhu.business.tasks.entity.VideoSynthTaskResult;
import com.lazhu.business.tasks.service.TasksService;
import com.lazhu.common.utils.OssUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 视频合成服务
 */
@Slf4j
@Service
public class ApIContentService {

    @Autowired
    private LLMService lLmService;

    @Autowired
    private MediaAssertsService mediaAssertsService;

    @Autowired
    private OssUtil ossUtil;



    @Qualifier("customTaskExecutor")
    @Autowired
    private ThreadPoolTaskExecutor customTaskExecutor;

    @Autowired
    private TasksService tasksService;

    /**
     * 视频合成入口方法
     *
     * @param param  视频创建参数
     * @param taskId 任务ID
     */
    public void createVideo(ContentCreationReq.VideoRequest param, String taskId) {
        //删除task
        TasksQuery query = new TasksQuery();
        query.setContentId(Convert.toLong(taskId));
        query.setType(3);
        List<Tasks> tasksList = tasksService.queryList(BeanUtil.beanToMap(query));
        tasksService.batchDelById(tasksList.stream().map(Tasks::getId).collect(Collectors.toList()));
        // 异步执行视频合成任务
        customTaskExecutor.execute(() -> createVideoSync(param, taskId));
    }

    /**
     * 同步执行视频合成任务
     *
     * @param param  视频创建参数
     * @param taskId 任务ID
     */
    private void createVideoSync(ContentCreationReq.VideoRequest param, String taskId) {

        try {
            // 1. 语音合成
            String audioUrl = processAudioSynthesis(param, taskId);

            // 2. AI对口型视频生成
            List<String> videoTaskIds = processVideoSynthesis(param, audioUrl);

            //taskId 保存至数据库，通过调度来执行查询对口型结果
            for (int i = 0; i < videoTaskIds.size(); i++) {
                Tasks tasks = new Tasks();
                tasks.setContentId(Convert.toLong(taskId));
                tasks.setType(3);
                tasks.setPriority(i);
                tasks.setTaskId(videoTaskIds.get(i));
                tasks.setStatus(1);
                tasksService.save(tasks);
            }

            log.info("视频创作完成，taskId: {}", taskId);
        } catch (Exception e) {
            log.error("视频创作失败，taskId: {}", taskId, e);
            Tasks tasks = new Tasks();
            tasks.setContentId(Convert.toLong(taskId));
            tasks.setType(3);
            tasks.setPriority(0);
            tasks.setTaskId(taskId);
            tasks.setStatus(3);
            VideoSynthTaskResult video = new VideoSynthTaskResult();
            video.setData(null);
            video.setMessage(e.getMessage());
            tasks.setResult(JSONObject.toJSONString(video));
            tasksService.save(tasks);
        }
    }

    /**
     * 处理音频合成
     */
    private String processAudioSynthesis(ContentCreationReq.VideoRequest param, String taskId) throws Exception {
        long startTime = System.currentTimeMillis();

        String voiceId = lLmService.queryVoiceId(param.getAudioSource(), param.getAudioId());
        String contentPath = lLmService.audioSynthesis(taskId, param.getContent(), voiceId);

        log.info("===> 音频合成完成，音频路径：{}，耗时：{} s", contentPath, (System.currentTimeMillis() - startTime) / 1000);

        return ossUtil.upload(FileUtil.file(contentPath + "/audio.mp3"), "video/" + taskId);
    }

    /**
     * 处理视频合成任务创建
     */
    private List<String> processVideoSynthesis(ContentCreationReq.VideoRequest param, String audioUrl) throws Exception {

        String assertsVideoUrl = mediaAssertsService.queryVideoUrl(param.getVideoSource(), param.getVideoId());
        LLMBaseResponse<VideoSynthesizeTask> resp = lLmService.videoSynthesis(audioUrl, assertsVideoUrl);
        List<String> taskIds = resp.getBody().getTaskIds();

        log.info("===> 对口型视频任务创建完成，taskIds：{}", taskIds);
        return taskIds;
    }

    /**
     * 字幕合成（暂未启用）
     */
//    private void srtCombine(ContentCreationReq.VideoRequest param, String taskId,
//                            VideoCombineResult taskInfo, RMap<String, String> taskMap,
//                            List<String> videoUrls, String contentPath) {
//        taskInfo.setCurStep(VideoCombineResult.Step.SC);
//        taskMap.put(taskId, JSONObject.toJSONString(taskInfo));
//
//        VideoCombineReq req = new VideoCombineReq();
//        req.setTaskId(taskId);
//        req.setTitleText(param.getTitle());
//        req.setVideoUrl(videoUrls);
//        req.setSrtPath(contentPath + "/audio.srt");
//        req.setOutputPath(contentPath + "/video_srt.mp4");
//
//        VideoCombineResult combineResult = videoCombineService.combine(req);
//        log.info("字幕合成任务创建完成，taskId：{}", combineResult.getTaskId());
//    }
}