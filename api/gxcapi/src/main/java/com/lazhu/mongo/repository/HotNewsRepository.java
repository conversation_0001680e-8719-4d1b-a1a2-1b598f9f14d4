package com.lazhu.mongo.repository;

import com.lazhu.mongo.entity.HotNews;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Repository
public interface HotNewsRepository extends ReactiveMongoRepository<HotNews, String> {

    @Query("{'topic': {'$regex': ?0, '$options': 'i'}}")
    Flux<HotNews> findByTopicContaining(String keyword);

    @Query("{}")
    Flux<HotNews> findAllOrderByHeatDesc(Pageable pageable);

    @Query("{'source': ?0}")
    Flux<HotNews> findBySourceOrderByHeatDesc(String source, Pageable pageable);

    @Query(value = "{}", fields = "{'source': 1}")
    Flux<HotNews> findDistinctSources();

    Mono<Long> countBySource(String source);

    // 新增：根据日期查询
    @Query("{'date': ?0}")
    Flux<HotNews> findByDateOrderByHeatDesc(String date, Pageable pageable);

    // 新增：根据日期计数
    Mono<Long> countByDate(String date);

    // 新增：根据关键字查询（支持分页）
    @Query("{'topic': {'$regex': ?0, '$options': 'i'}}")
    Flux<HotNews> findByTopicContainingOrderByHeatDesc(String keyword, Pageable pageable);

    // 新增：根据关键字计数
    @Query(value = "{'topic': {'$regex': ?0, '$options': 'i'}}", count = true)
    Mono<Long> countByTopicContaining(String keyword);

    // 新增：根据日期和关键字查询
    @Query("{'$and': [" +
            "{'date': ?0}, " +
            "{'topic': {'$regex': ?1, '$options': 'i'}}" +
            "]}")
    Flux<HotNews> findByDateAndKeywordOrderByHeatDesc(String date, String keyword, Pageable pageable);

    // 新增：根据日期和关键字计数
    @Query(value = "{'$and': [" +
            "{'date': ?0}, " +
            "{'topic': {'$regex': ?1, '$options': 'i'}}" +
            "]}", count = true)
    Mono<Long> countByDateAndKeyword(String date, String keyword);

    // 新增：根据来源、日期和关键字查询
    @Query("{'$and': [" +
            "{'source': ?0}, " +
            "{'date': ?1}, " +
            "{'topic': {'$regex': ?2, '$options': 'i'}}" +
            "]}")
    Flux<HotNews> findBySourceAndDateAndKeywordOrderByHeatDesc(String source, String date, String keyword, Pageable pageable);

    // 新增：根据来源、日期和关键字计数
    @Query(value = "{'$and': [" +
            "{'source': ?0}, " +
            "{'date': ?1}, " +
            "{'topic': {'$regex': ?2, '$options': 'i'}}" +
            "]}", count = true)
    Mono<Long> countBySourceAndDateAndKeyword(String source, String date, String keyword);

    // 新增：根据来源和日期查询
    @Query("{'$and': [" +
            "{'source': ?0}, " +
            "{'date': ?1}" +
            "]}")
    Flux<HotNews> findBySourceAndDateOrderByHeatDesc(String source, String date, Pageable pageable);

    // 新增：根据来源和日期计数
    @Query(value = "{'$and': [" +
            "{'source': ?0}, " +
            "{'date': ?1}" +
            "]}", count = true)
    Mono<Long> countBySourceAndDate(String source, String date);

    // 新增：根据来源和关键字查询（支持分页）
    @Query("{'$and': [" +
            "{'source': ?0}, " +
            "{'topic': {'$regex': ?1, '$options': 'i'}}" +
            "]}")
    Flux<HotNews> findBySourceAndKeywordOrderByHeatDesc(String source, String keyword, Pageable pageable);

    // 新增：根据来源和关键字计数
    @Query(value = "{'$and': [" +
            "{'source': ?0}, " +
            "{'topic': {'$regex': ?1, '$options': 'i'}}" +
            "]}", count = true)
    Mono<Long> countBySourceAndKeyword(String source, String keyword);

}