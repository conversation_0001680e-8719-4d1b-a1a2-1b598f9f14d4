package com.lazhu.mongo.service;

import com.lazhu.mongo.entity.HotNews;
import com.lazhu.mongo.repository.HotNewsRepository;
import com.lazhu.system.sysparams.service.SysParamsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

@Service
public class HotNewsService {

    @Autowired
    private HotNewsRepository hotNewsRepository;

    @Autowired
    private SysParamsService sysParamsService;

    public Flux<HotNews> findHotNewsList(int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "heat"));
        return hotNewsRepository.findAllOrderByHeatDesc(pageable);
    }

    public Flux<HotNews> findHotNewsBySource(String source, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "heat"));
        return hotNewsRepository.findBySourceOrderByHeatDesc(source, pageable);
    }

    public Flux<HotNews> searchNewsByTopic(String keyword) {
        return hotNewsRepository.findByTopicContaining(keyword);
    }


    // 新增：根据来源和关键字检索（用于search接口，不分页）
    public Flux<HotNews> searchNewsBySourceAndKeyword(String source, String keyword) {
        // 使用分页方法但不限制数量，通过排序获取所有结果
        Pageable pageable = PageRequest.of(0, Integer.MAX_VALUE, Sort.by(Sort.Direction.DESC, "heat"));
        return hotNewsRepository.findBySourceAndKeywordOrderByHeatDesc(source, keyword, pageable);
    }

    public Flux<String> findAllSources() {
        String s = sysParamsService.queryByKey("hot_new_source_index");
        List<String> sourceIndex = Arrays.asList(s.split(","));
        //按配置的索引位置排序，没配置的排最后
        Comparator<String> comparator = (o1, o2) -> {
            int index1 = sourceIndex.indexOf(o1);
            index1 = index1 == -1 ? 100 : index1;
            int index2 = sourceIndex.indexOf(o2);
            index2 = index2 == -1 ? 100 : index2;
            // 按索引位置排序
            return Integer.compare(index1, index2);
        };
        return hotNewsRepository.findDistinctSources().map(HotNews::getSource).distinct().sort(comparator);
    }

    public Mono<Long> countHotNews() {
        return hotNewsRepository.count();
    }

    public Mono<Long> countHotNewsBySource(String source) {
        return hotNewsRepository.countBySource(source);
    }

    // 新增：根据日期查询热点新闻
    public Flux<HotNews> findHotNewsByDate(String date, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "heat"));
        return hotNewsRepository.findByDateOrderByHeatDesc(date, pageable);
    }

    // 新增：根据日期计数
    public Mono<Long> countHotNewsByDate(String date) {
        return hotNewsRepository.countByDate(date);
    }

    // 新增：根据关键字查询热点新闻（支持分页）
    public Flux<HotNews> findHotNewsByKeyword(String keyword, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "heat"));
        return hotNewsRepository.findByTopicContainingOrderByHeatDesc(keyword, pageable);
    }

    // 新增：根据关键字计数
    public Mono<Long> countHotNewsByKeyword(String keyword) {
        return hotNewsRepository.countByTopicContaining(keyword);
    }

    // 新增：根据日期和关键字查询热点新闻
    public Flux<HotNews> findHotNewsByDateAndKeyword(String date, String keyword, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "heat"));
        return hotNewsRepository.findByDateAndKeywordOrderByHeatDesc(date, keyword, pageable);
    }

    // 新增：根据日期和关键字计数
    public Mono<Long> countHotNewsByDateAndKeyword(String date, String keyword) {
        return hotNewsRepository.countByDateAndKeyword(date, keyword);
    }

    // 新增：根据来源、日期和关键字查询热点新闻
    public Flux<HotNews> findHotNewsBySourceAndDateAndKeyword(String source, String date, String keyword, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "heat"));
        return hotNewsRepository.findBySourceAndDateAndKeywordOrderByHeatDesc(source, date, keyword, pageable);
    }

    // 新增：根据来源、日期和关键字计数
    public Mono<Long> countHotNewsBySourceAndDateAndKeyword(String source, String date, String keyword) {
        return hotNewsRepository.countBySourceAndDateAndKeyword(source, date, keyword);
    }

    // 新增：根据来源和日期查询热点新闻
    public Flux<HotNews> findHotNewsBySourceAndDate(String source, String date, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "heat"));
        return hotNewsRepository.findBySourceAndDateOrderByHeatDesc(source, date, pageable);
    }

    // 新增：根据来源和日期计数
    public Mono<Long> countHotNewsBySourceAndDate(String source, String date) {
        return hotNewsRepository.countBySourceAndDate(source, date);
    }

    // 新增：根据来源和关键字查询热点新闻（支持分页）
    public Flux<HotNews> findHotNewsBySourceAndKeyword(String source, String keyword, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "heat"));
        return hotNewsRepository.findBySourceAndKeywordOrderByHeatDesc(source, keyword, pageable);
    }

    // 新增：根据来源和关键字计数
    public Mono<Long> countHotNewsBySourceAndKeyword(String source, String keyword) {
        return hotNewsRepository.countBySourceAndKeyword(source, keyword);
    }
}