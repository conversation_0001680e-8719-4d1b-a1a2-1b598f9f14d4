package com.lazhu.mongo.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Document(collection = "hotnews")
public class HotNews implements Serializable {
    
    @Id
    private String id;
    
    @Field("date")
    private String date;
    
    @Field("topic")
    private String topic;
    
    @Field("source")
    private String source;
    
    @Field("kind")
    private String kind;
    
    @Field("heat")
    private String heat;

    @Field("url")
    private String url;
}