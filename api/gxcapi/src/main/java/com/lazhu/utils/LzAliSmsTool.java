package com.lazhu.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.lazhu.common.LzSmsConst.*;

/**
 * 阿里云短信服务 (蜡烛的阿里云账户)
 *
 * <AUTHOR>
 * @date 2018-02-08
 */
@Slf4j
@Component
public class LzAliSmsTool {

    private IAcsClient client = null;

    private final String domain = "dysmsapi.aliyuncs.com";// 短信API产品域名（接口地址固定，无需修改）


    @PostConstruct
    private void init() {
        IClientProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        client = new DefaultAcsClient(profile);
    }

    /**
     * 发送短信
     */
    public void sendSms(String sign, String templateCode, String numbers, String contents) {
        try {
            CommonRequest request = new CommonRequest();
            request.setSysMethod(MethodType.POST);
            request.setSysDomain(domain);
            request.setSysVersion("2017-05-25");
            request.setSysAction("SendSms");
            request.putQueryParameter("RegionId", "cn-hangzhou");
            request.putQueryParameter("PhoneNumbers", numbers);
            request.putQueryParameter("SignName", sign);
            request.putQueryParameter("TemplateCode", templateCode);
            request.putQueryParameter("TemplateParam", contents);
            CommonResponse response = client.getCommonResponse(request);
            log.info("发送短信成功 >>> 手机号：{},content:{},resp:{}", numbers, contents, response.getData());
        } catch (Exception e) {
            log.error("发送短信失败 >>> 手机号：{},content:{}", numbers, contents);
            log.error("发送短信失败 >>> ", e);
        }
    }


    /**
     * 发送验证码短信
     *
     * @param sign    签名
     * @param numbers 手机号码
     * @param code    验证码
     */
    public void sendCodeSms(String sign, String numbers, String code) {
        JSONObject obj = new JSONObject();
        obj.put("code", code);
        this.sendSms(sign, codeTemplate, numbers, obj.toJSONString());
    }
}
