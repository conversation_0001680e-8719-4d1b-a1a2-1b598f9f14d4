package com.lazhu.utils;

import cn.hutool.core.util.StrUtil;
import com.lazhu.common.Const;
import com.lazhu.filter.TokenProp;
import com.lazhu.support.base.BaseToken;
import com.lazhu.support.config.SpringContext;
import com.lazhu.support.util.JwtTokenUtil;
import com.lazhu.support.util.WebUtil;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

public class WebTool {

    public static Long getUserId() {
        String tk = WebUtil.getHeader(Const.SESSION_TOKEN);
        if (StrUtil.isBlank(tk)) {
            return null;
        }
        TokenProp tp = SpringContext.getBean(TokenProp.class);
        BaseToken bt = JwtTokenUtil.getToken(tk, tp.getSecret(), BaseToken.class);
        if (bt == null) {
            return null;
        }
        return Long.valueOf(bt.getUserId());
    }

    public static String getRequestURI(){
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = requestAttr.getRequest();
        return request.getRequestURI();
    }
}
