package com.lazhu.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 蜡烛云Oss文件上传常量类
 */
@Configuration
public class LzOssConfig {

    @Value("${lz.upload.ossBucket:}")
    private String ossBucket;
    @Value("${lz.upload.ossWebSite:}")
    private String ossWebSite;
    @Value("${lz.upload.ossBaseKey:}")
    private String ossBaseKey;


    private static final String endpoint = "https://oss-cn-hangzhou.aliyuncs.com";
    private static final String accessKey = "LTAI5tAeMJfLqWjvVgnEXy3b";
    private static final String accessKeySecret = "******************************";

    @Bean
    public OSS oss() {
        return new OSSClient(endpoint, new DefaultCredentialProvider(accessKey, accessKeySecret),null);
    }
}
