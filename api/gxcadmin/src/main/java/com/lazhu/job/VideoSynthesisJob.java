package com.lazhu.job;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lazhu.business.content.entity.Content;
import com.lazhu.business.content.service.ContentService;
import com.lazhu.business.tasks.entity.Tasks;
import com.lazhu.business.tasks.entity.TasksQuery;
import com.lazhu.business.tasks.entity.VideoSynthTaskResult;
import com.lazhu.business.tasks.service.TasksService;
import com.lazhu.common.utils.MediaUtil;
import com.lazhu.common.utils.OssUtil;
import com.lazhu.baseai.llm.dto.LLMBaseResponse;
import com.lazhu.baseai.llm.dto.VideoSynthesizeResp;
import com.lazhu.business.llmconfig.service.LLMService;
import com.lazhu.support.config.SpringContext;
import jodd.util.Task;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * 视频合成job
 */
@Slf4j
@Component
public class VideoSynthesisJob {

    @Autowired
    private ContentService contentService;


    @Autowired
    private LLMService lLMService;

    @Autowired
    private TasksService taskService;

    @Autowired
    private OssUtil ossUtil;

    @Autowired
    private VideoSynthesisJob job;


    /**
     * 查询视频合成状态
     */
    @Scheduled(fixedDelay = 30 * 1000L)

    public void queryVideoSynthesisStatus() {
        log.info("===> 开始对口型视频任务合成完成");
        // 查询未完成的视频合成任务
        TasksQuery query = new TasksQuery();
        query.setType(3);
        query.setStatus(1);
        List<Tasks> tasksList = taskService.queryList(BeanUtil.beanToMap(query));
        for (Tasks task : tasksList) {
            try {
                job.processTask(task);
                //未完成 继续查询
            } catch (Exception e) {
                log.error("视频合成任务查询异常：" + task.getId(), e);
            }
        }
        log.info("===> 对口型视频任务合成完成，size：{} ", tasksList.size());
    }

    @Transactional(rollbackFor = Exception.class)
    protected void processTask(Tasks task) {
        String taskId = task.getTaskId();
        LLMBaseResponse<VideoSynthesizeResp> res = lLMService.queryVideoTask(taskId);
        VideoSynthesizeResp body = res.getBody();
        if (body.getStatus().equals("SUCCEEDED")) {
            task.setStatus(2);
            VideoSynthTaskResult taskResult = new VideoSynthTaskResult();
            taskResult.setData(body);
            task.setResult(JSONObject.toJSONString(taskResult));
            taskService.update(task);
            //检查视频是否都合成完成
            combineVideo(task);

        } else if (body.getStatus().equals("FAILED") || body.getStatus().equals("UNKNOWN")) {
            task.setStatus(3);
            VideoSynthTaskResult taskResult = new VideoSynthTaskResult();
            taskResult.setCode(body.getCode());
            taskResult.setMessage(body.getMsg());
            taskResult.setData(body);
            task.setResult(JSONObject.toJSONString(taskResult));
            taskService.update(task);
        }
    }


    private void combineVideo(Tasks task) {
        //获取视频链接
        Long contentId = task.getContentId();
        TasksQuery query = new TasksQuery();
        query.setContentId(contentId);
        query.setType(3);
        query.setExcludeId(task.getId());
        List<Tasks> tasksList = taskService.queryList(BeanUtil.beanToMap(query));
        tasksList.add(task);
        //检查任务是否全部完成
        boolean isAllCompleted = tasksList.stream().allMatch(task1 -> task1.getStatus() == 2);
        if (!isAllCompleted) {
            return;
        }
        // 根据优先级排序
        tasksList.sort(Comparator.comparingInt(Tasks::getPriority));
        List<String> videoUrls = tasksList.stream().map(e -> {
            String result = e.getResult();
            return JSONObject.parseObject(result, VideoSynthTaskResult.class).getData().getVideoUrl();
        }).toList();

        // 视频合成
        String videoUrl = mergeAndUploadVideos(videoUrls, contentId.toString());
        // 更新内容
        Content content = new Content();
        content.setId(contentId);
        content.setMediaUrl(videoUrl);

        File coverImageFile = MediaUtil.getVideoCover(videoUrl);
        String coverImage = ossUtil.upload(coverImageFile);
        content.setCoverImg(JSONArray.toJSONString(Collections.singletonList(coverImage)));
        content.setDuration(MediaUtil.getDuration(videoUrl));

        contentService.update(content);
        FileUtil.del(coverImageFile); // 清理封面图片临时文件
    }

    /**
     * 合并并上传视频
     */
    private String mergeAndUploadVideos(List<String> videoUrls, String taskId) {
        if (CollectionUtil.isEmpty(videoUrls)) {
            throw new RuntimeException("视频URL列表为空");
        }

        String url;
        if (CollectionUtil.size(videoUrls) > 1) {
            // 多个视频需要合并
            String tempPath = FileUtil.getTmpDirPath();
            if(tempPath.endsWith(File.separator)){
                tempPath = tempPath + taskId;
            } else {
                tempPath = tempPath + File.separator + taskId;
            }
            MediaUtil.merge(videoUrls, tempPath + "/video.mp4");
            url = ossUtil.upload(FileUtil.file(tempPath + "/video.mp4"), "video/" + taskId);
            FileUtil.del(tempPath); // 清理临时文件
        } else {
            // 单个视频直接上传
            String videoUrl = videoUrls.getFirst();
            url = ossUtil.upload(videoUrl, "video/" + taskId + "/video.mp4");
        }

        return url;
    }

}
