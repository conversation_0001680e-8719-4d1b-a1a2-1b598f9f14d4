package com.lazhu.system.sysmenus.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.lazhu.system.sysperms.entity.SysPerms;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
@Data
public class MenuTree implements Serializable {

	private static final long serialVersionUID = 1L;

	@JSONField(serializeUsing = ToStringSerializer.class)
	private Long id;

	private String name;

	private String ico;

	private String menuUrl;

	private Integer level;

	private String code;

	private Integer orderNum;

	@JSONField(serializeUsing = ToStringSerializer.class)
	private Long parentId;

	private List<MenuTree> children;

	private List<SysPerms> perms;

	public MenuTree() {
		super();
	}

	public MenuTree(Long menuId, String menuName, Long parentId, Integer level, List<MenuTree> children) {
		super();
		this.id = menuId;
		this.name = menuName;
		this.level = level;
		this.children = children;
		this.parentId = parentId;
	}

	public MenuTree(SysMenus menu, List<MenuTree> children,List<SysPerms> perms) {
		super();
		this.id = menu.getId();
		this.name = menu.getName();
		this.level = menu.getLevel();
		this.children = children;
		this.parentId = menu.getParentId();
		this.ico = menu.getIco();
		this.menuUrl = menu.getMenuUrl();
		this.orderNum = menu.getOrderNum();
		this.code = menu.getCode();
		this.perms = perms;
	}

	public void addChild(MenuTree children) {
		this.children.add(children);
	}

}
