package com.lazhu.system.sysmenus.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import com.baomidou.mybatisplus.core.toolkit.BeanUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.support.base.BaseService;
import com.lazhu.system.login.entity.MenuItem;
import com.lazhu.system.login.entity.MenuQuery;
import com.lazhu.system.sysmenus.entity.MenuTree;
import com.lazhu.system.sysmenus.entity.SysMenus;
import com.lazhu.system.sysmenus.mapper.SysMenusMapper;
import com.lazhu.system.sysperms.entity.SysPerms;
import com.lazhu.system.sysperms.mapper.SysPermsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Primary
public class SysMenusService extends BaseService<SysMenus> {

    @Autowired
    private SysMenusMapper mapper;

    @Autowired
    private SysPermsMapper sysPermsMapper;

    public MenuTree queryMenuTree() {
        List<SysMenus> menus = mapper.queryMenus();
        Map<Long, List<SysPerms>> permsMap = queryPerms();
        LinkedHashMap<Long, MenuTree> level1 = new LinkedHashMap<Long, MenuTree>();
        List<MenuTree> level2 = new ArrayList<>();
        menus.stream().map(
                        e -> new MenuTree(e, new ArrayList<MenuTree>(), permsMap.get(e.getId())))
                .forEach(e -> {
                    if (e.getLevel() == 1) {
                        level1.put(e.getId(), e);
                    } else if (e.getLevel() == 2) {
                        level2.add(e);
                    }
                });
        level2.stream().forEach(e -> {
            if (e.getParentId() != null) {
                MenuTree mt = level1.get(e.getParentId());
                if (mt != null) {
                    mt.addChild(e);
                }
            }
        });
        MenuTree mt = new MenuTree(0L, "根目录", null, 0, new ArrayList<MenuTree>());
        level1.entrySet().forEach(e -> {
            mt.addChild(e.getValue());
        });
        return mt;
    }

    private Map<Long,List<SysPerms>> queryPerms() {
        HashMap<String, Object> param = new HashMap<>();
        param.put("enable", 1);
        List<SysPerms> sysPerms = sysPermsMapper.selectIdPage(param);
        //根据菜单Id分组
        return sysPerms.stream().collect(Collectors.groupingBy(SysPerms::getMenuId));
    }

    public Page<SysMenus> page(MenuQuery params) {
        Map<String, Object> param = BeanUtils.beanToMap(params);
        return queryPage(param);
    }

    public List<MenuItem> queryIndexMenus(Long userId, boolean curUserSuper) {
        List<SysMenus> menus1;
        if (curUserSuper) {
            Map<String, Object> params = new HashMap<>();
            params.put("enable", 1);
            menus1 = queryList(params);
        } else {
            menus1 = mapper.queryByUser(userId);
        }

        if (menus1 == null || menus1.isEmpty()) {
            return Collections.emptyList();
        }

        // 使用 TreeUtil 构建树形结构
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setIdKey("id");
        treeNodeConfig.setParentIdKey("parentId");
        treeNodeConfig.setChildrenKey("children");
        treeNodeConfig.setDeep(3);
        treeNodeConfig.setWeightKey("orderNum");


        List<Tree<Long>> treeList = TreeUtil.build(menus1, 0L,treeNodeConfig, (sysMenu, treeNode) -> {
            treeNode.setId(sysMenu.getId());
            treeNode.setParentId(sysMenu.getParentId());
            treeNode.putExtra("name", sysMenu.getName());
            treeNode.putExtra("menuUrl", sysMenu.getMenuUrl());
            treeNode.putExtra("ico", sysMenu.getIco());
            treeNode.putExtra("orderNum", sysMenu.getOrderNum());
        });

        // 转换为 MenuItem 树形结构
        return treeList.stream()
                .map(this::toMenuItem)
                .sorted(Comparator.comparing(MenuItem::getOrderNum))
                .collect(Collectors.toList());
    }

    private MenuItem toMenuItem(Tree<Long> tree) {
        List<MenuItem> children ;
        if (CollUtil.isNotEmpty(tree.getChildren())) {
            children = tree.getChildren().stream()
                    .map(this::toMenuItem)
                    .sorted(Comparator.comparing(MenuItem::getOrderNum))
                    .collect(Collectors.toList());
        }  else {
            children = Collections.emptyList();
        }

        return new MenuItem(
                tree.getId(),
                tree.get("name").toString(),
                tree.get("menuUrl").toString(),
                tree.get("ico").toString(),
                tree.getParentId(),
                Convert.toInt(tree.get("orderNum")),
                children
        );
    }
}
