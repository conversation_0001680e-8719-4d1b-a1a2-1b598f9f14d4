package com.lazhu.system.sysmenus.web;

import com.lazhu.support.base.BaseController;
import com.lazhu.support.response.*;
import com.lazhu.system.sysmenus.entity.MenuTree;
import com.lazhu.system.sysmenus.entity.SysMenus;
import com.lazhu.system.sysmenus.entity.SysMenusDelQuery;
import com.lazhu.system.sysmenus.entity.SysMenusQuery;
import com.lazhu.system.sysmenus.service.SysMenusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-07
 */
@RestController
@RequestMapping(value = "/system/sysMenus")
public class SysMenusController extends BaseController<SysMenus> {

	@Autowired
	SysMenusService service;

	@GetMapping("/read/menuTree")
	public SimpleResponse<MenuTree> menuTree() {
		MenuTree mts = service.queryMenuTree();
		return new SimpleResponseBuilder<MenuTree>(mts).success().bulider();
	}

	@PostMapping(value = "/read/list")
	public PageResponse<SysMenus> query(@RequestBody SysMenusQuery param) {
		param.setEnable(1);
		return super.query(param);
	}

	@Override
    @GetMapping(value = "/read/detail")
	public SimpleResponse<SysMenus> get(@RequestParam Long id) {
		Assert.notNull(id, "ID");
		return super.get(id);
	}

	@PostMapping("/add")
	public SimpleResponse<SysMenus> add(@RequestBody SysMenus param) throws InterruptedException {
		if (param.getId() != null) {
			throw new InterruptedException("add element not need  id !");
		}
		return super.save(param);
	}

	@PostMapping("/edit")
	public SimpleResponse<SysMenus> edit(@RequestBody SysMenus param) throws InterruptedException {
		if (param.getId() == null) {
			throw new InterruptedException("update element need  id !");
		}
		return super.update(param);
	}

	@GetMapping("/del")
	public SimpleResponse<SysMenus> del(@RequestParam Long id) {
		Assert.notNull(id, "ID");
		return super.delete(id);
	}

	@PostMapping(value = "/batchDel")
	public SimpleResponse<SysMenus> batchDel(@RequestBody SysMenusDelQuery querys) {
		Assert.notNull(querys.getIds(), "IDS");
		return super.delete(querys.getIds());
	}
}
