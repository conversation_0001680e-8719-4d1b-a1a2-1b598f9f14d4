package com.lazhu.support.util;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import com.lazhu.support.config.SpringContext;
import com.lazhu.support.security.LZUserDetail;
import com.lazhu.system.sysusers.entity.SysUsers;
import com.lazhu.system.sysusers.service.SysUsersService;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * <AUTHOR>
 *
 */
public class WebUtil {

	public static final String USER_DETAIL = "user";

	public static final String SUPER = "super";

	public static <T> void setAttr(String key, T data) {
		StpUtil.getSession().set(key, data);
	}

	@SuppressWarnings("unchecked")
	public static <T> T getAttr(String key) {
		Object obj = StpUtil.getSession().get(key);
		if (obj == null) {
			return null;
		}
		return (T) obj;
	}

	public static void remove(String key) {
		StpUtil.getSession().delete(key);
	}

	public static String getRequestIp() {
		ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		HttpServletRequest request = requestAttr.getRequest();
		return JakartaServletUtil.getClientIP(request, "");
	}

	public static String getRequestUri() {
		ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		HttpServletRequest request = requestAttr.getRequest();
		return request.getRequestURI();
	}

	public static Long getCurUser() {
		LZUserDetail curUserDetail = getCurUserDetail();
		SysUsersService usersService = SpringContext.getBean(SysUsersService.class);
		SysUsers sysUsers = usersService.queryByAccount(curUserDetail.getAccount());
		if (sysUsers == null) {
			return null;
		}
		return sysUsers.getId();
	}

	public static LZUserDetail getCurUserDetail() {
		return getAttr(USER_DETAIL);
	}

	public static void saveCurUser(LZUserDetail detail) {
		setAttr(USER_DETAIL, detail);
	}

	public static void removeCurUser() {
		remove(USER_DETAIL);
	}

	public static boolean getCurUserSuper() {
		LZUserDetail userDetail = getCurUserDetail();
		for (String a : userDetail.getRoles()) {
			if (SUPER.equals(a)) {
				return true;
			}
		}
		return false;
	}

}
