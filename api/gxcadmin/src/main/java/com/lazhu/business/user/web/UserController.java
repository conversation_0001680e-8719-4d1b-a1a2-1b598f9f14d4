package com.lazhu.business.user.web;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.business.user.dto.UserDetailDTO;
import com.lazhu.business.user.entity.User;
import com.lazhu.business.user.entity.UserQuery;
import com.lazhu.business.user.service.UserService;
import com.lazhu.common.utils.ComplexMD5Util;
import com.lazhu.common.utils.KmsSecretUtil;
import com.lazhu.common.exception.BusinessException;
import com.lazhu.support.base.BaseController;
import com.lazhu.support.response.PageResponse;
import com.lazhu.support.response.PageResponseBuilder;
import com.lazhu.support.response.SimpleResponse;
import com.lazhu.support.response.SimpleResponseBuilder;
import com.lazhu.support.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 用户管理
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@RestController
@RequestMapping(value = "bus/user")
public class UserController extends BaseController<User> {

    @Autowired
    private UserService userService;

    @PostMapping(value = "/read/list")
    public PageResponse<UserDetailDTO> page(@RequestBody UserQuery param) {
        if (StrUtil.isNotBlank(param.getMobile())) {
            param.setMobileMd5(ComplexMD5Util.MD5Encode(param.getMobile()));
            param.setMobile(null);
        }
        param.setUserStatus("1");
        Map<String, Object> params = BeanUtil.beanToMap(param);
        Page<User> userPage = userService.queryPage(params);
        List<UserDetailDTO> userDetailDTOS = new ArrayList<>();
        for (User user : userPage.getRecords()) {
            UserDetailDTO userDetailDTO = new UserDetailDTO();
            BeanUtil.copyProperties(user, userDetailDTO);
            userDetailDTO.setMobileMask(StringUtils.fuzzMobile(KmsSecretUtil.decrypt(user.getMobile())));
            userDetailDTOS.add(userDetailDTO);
        }
        Page<UserDetailDTO> page = new Page<>();
        BeanUtil.copyProperties(userPage, page, "records");
        page.setRecords(userDetailDTOS);
        return new PageResponseBuilder<>(page).success().builder();
    }

    @GetMapping(value = "/read/detail")
    public SimpleResponse<UserDetailDTO> detail(Long id) {
        Assert.notNull(id, "ID");
        User user = service.queryById(id);
        UserDetailDTO result = new UserDetailDTO();
        BeanUtil.copyProperties(user, result);
        result.setMobileMask(StringUtils.fuzzMobile(KmsSecretUtil.decrypt(user.getMobile())));
        return new SimpleResponseBuilder<>(result).success().bulider();
    }

    @PostMapping("/add")
    public SimpleResponse<User> add(@RequestBody User param) throws InterruptedException {
        if (param.getId() != null) {
            throw new InterruptedException("add element not need  id !");
        }
        if (param.getMobile() != null && Validator.isMobile(param.getMobile())) {
            User user = userService.queryByMobile(param.getMobile());
            if (user != null) {
                throw new BusinessException("手机号已存在");
            }
            param.setMobileMd5(ComplexMD5Util.MD5Encode(param.getMobile()));
            param.setUserStatus("1");
            param.setMobile(KmsSecretUtil.encrypt(param.getMobile()));
        }
        return super.save(param);
    }

    @PostMapping("/edit")
    public SimpleResponse<User> edit(@RequestBody User param) throws InterruptedException {
        if (param.getId() == null) {
            throw new InterruptedException("update element need  id !");
        }
        if (param.getMobile() != null && Validator.isMobile(param.getMobile())) {
            User user = userService.queryByMobile(param.getMobile());
            if (user != null && !user.getId().equals(param.getId())) {
                throw new BusinessException("手机号已存在");
            }
            param.setMobileMd5(ComplexMD5Util.MD5Encode(param.getMobile()));
            param.setMobile(KmsSecretUtil.encrypt(param.getMobile()));
        } else {
            //不更新
            param.setMobile(null);
            param.setMobileMd5(null);
        }
        return super.update(param);
    }

    @GetMapping("/del")
    public SimpleResponse<User> del(Long id) {
        Assert.notNull(id, "ID");
        User user = userService.queryById(id);
        user.setUserStatus("0");
        return super.update(user);
    }
}
