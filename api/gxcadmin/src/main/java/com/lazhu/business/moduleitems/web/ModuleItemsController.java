package com.lazhu.business.moduleitems.web;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.stereotype.Controller;

import org.springframework.util.Assert;

import com.alibaba.fastjson.JSONArray;
import com.lazhu.support.base.BaseController;
import com.lazhu.support.response.PageResponse;
import com.lazhu.support.response.SimpleResponse;
import com.lazhu.business.moduleitems.entity.ModuleItems;
import com.lazhu.business.moduleitems.entity.ModuleItemsQuery;

/**
 * <p>
 * 模块元素配置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@RestController
@RequestMapping(value = "business/moduleItems")
public class ModuleItemsController extends BaseController<ModuleItems> {
	
	@PostMapping(value = "/read/list")
	public PageResponse<ModuleItems> query(@RequestBody ModuleItemsQuery param) {
		return super.query(param);
	}

	@GetMapping(value = "/read/detail")
	public SimpleResponse<ModuleItems> get(Long id) {
		Assert.notNull(id, "ID");
		return super.get(id);
	}

	@PostMapping("/add")
	public SimpleResponse<ModuleItems> add(@RequestBody ModuleItems param) throws InterruptedException{
	    if (param.getId() != null) {
			throw new InterruptedException("add element not need  id !");
		}
		return super.save(param);
	}

	@PostMapping("/edit")
	public SimpleResponse<ModuleItems> edit(@RequestBody ModuleItems param) throws InterruptedException{
		if (param.getId() == null) {
			throw new InterruptedException("update element need  id !");
		}
		return super.update(param);
	}

	@GetMapping("/del")
	public SimpleResponse<ModuleItems> del(Long id) {
		Assert.notNull(id, "ID");
		return super.delete(id);
	}
}
