package com.lazhu.business.user.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class UserDetailDTO implements Serializable {


    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    private String nickName;

    private String headImg;

    private String mobileMask;

    private Date createTime;


}
