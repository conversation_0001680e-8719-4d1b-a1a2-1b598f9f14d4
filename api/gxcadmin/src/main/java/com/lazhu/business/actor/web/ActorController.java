package com.lazhu.business.actor.web;

import com.alibaba.fastjson.JSONObject;
import com.lazhu.baseai.tool.txt.PromptUtil;
import com.lazhu.support.response.SimpleResponseBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import org.springframework.util.Assert;

import com.lazhu.support.base.BaseController;
import com.lazhu.support.response.PageResponse;
import com.lazhu.support.response.SimpleResponse;
import com.lazhu.business.actor.entity.Actor;
import com.lazhu.business.actor.entity.ActorQuery;

/**
 * <p>
 * 演员表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@RestController
@RequestMapping(value = "bus/actor")
public class ActorController extends BaseController<Actor> {


	@Autowired
	private PromptUtil promptUtil;

	@PostMapping(value = "/read/list")
	public PageResponse<Actor> query(@RequestBody ActorQuery param) {
		param.setUseStatus(1);
		return super.query(param);
	}

	@GetMapping(value = "/read/detail")
	public SimpleResponse<Actor> get(Long id) {
		Assert.notNull(id, "ID");
		return super.get(id);
	}

	@PostMapping("/add")
	public SimpleResponse<Actor> add(@RequestBody Actor param) throws InterruptedException{
	    if (param.getId() != null) {
			throw new InterruptedException("add element not need  id !");
		}
		return super.save(param);
	}

	@PostMapping("/edit")
	public SimpleResponse<Actor> edit(@RequestBody Actor param) throws InterruptedException{
		if (param.getId() == null) {
			throw new InterruptedException("update element need  id !");
		}
		return super.update(param);
	}

	@GetMapping("/del")
	public SimpleResponse<Actor> del(Long id) {
		Assert.notNull(id, "ID");
		Actor actor = this.service.queryById(id);
		actor.setUseStatus(0);
		return super.update(actor);
	}

	/**
	 * 生成提示词
	 */
	@PostMapping("/createPrompt")
	public SimpleResponse<String> createPrompt(@RequestBody JSONObject param) {
		String profile = param.getString("profile");
		String prompt = promptUtil.createPrompt(profile);
		return new SimpleResponseBuilder<>(prompt).success().bulider();
	}

}
