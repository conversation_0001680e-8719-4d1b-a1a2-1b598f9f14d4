package com.lazhu.business.actorconfig.web;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import org.springframework.util.Assert;

import com.lazhu.support.base.BaseController;
import com.lazhu.support.response.PageResponse;
import com.lazhu.support.response.SimpleResponse;
import com.lazhu.business.actorconfig.entity.ActorConfig;
import com.lazhu.business.actorconfig.entity.ActorConfigQuery;

/**
 * <p>
 * 人物配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@RestController
@RequestMapping(value = "bus/ac")
public class ActorConfigController extends BaseController<ActorConfig> {

    @PostMapping(value = "/read/list")
    public PageResponse<ActorConfig> query(@RequestBody ActorConfigQuery param) {
        param.setSource("1");
        return super.query(param);
    }

    @GetMapping(value = "/read/detail")
    public SimpleResponse<ActorConfig> get(Long id) {
        Assert.notNull(id, "ID");
        return super.get(id);
    }

    @PostMapping("/add")
    public SimpleResponse<ActorConfig> add(@RequestBody ActorConfig param) throws InterruptedException {
        if (param.getId() != null) {
            throw new InterruptedException("add element not need  id !");
        }
        param.setSource("1");
        return super.save(param);
    }

    @PostMapping("/edit")
    public SimpleResponse<ActorConfig> edit(@RequestBody ActorConfig param) throws InterruptedException {
        if (param.getId() == null) {
            throw new InterruptedException("update element need  id !");
        }
        param.setSource("1");
        return super.update(param);
    }

    @GetMapping("/del")
    public SimpleResponse<ActorConfig> del(Long id) {
        Assert.notNull(id, "ID");
        return super.delete(id);
    }
}
