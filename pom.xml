<project
	xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.lazhu</groupId>
		<artifactId>torch-ops</artifactId>
		<version>3.0.0</version>
	</parent>
	<artifactId>galaxycreator</artifactId>
	<version>1.0.0</version>
	<packaging>pom</packaging>
	<modules>
		<module>base/baseai</module>
		<module>api/gxcapi</module>
		<module>base/basebus</module>
		<module>api/gxcadmin</module>
		<module>base/gxc-publish</module>
        <module>base/gxc-common</module>
    </modules>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.ai</groupId>
				<artifactId>spring-ai-bom</artifactId>
				<version>1.0.0</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>dashscope-sdk-java</artifactId>
				<version>2.20.4</version>
			</dependency>
			<dependency>
				<groupId>cn.hutool</groupId>
				<artifactId>hutool-all</artifactId>
				<version>5.8.35</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<repositories>
		<repository>
			<layout>default</layout>
			<id>nexus-lazhu</id>
			<name>nexus-lazhu</name>
			<url>https://nexus.wts99.cn/repository/maven-public</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>spring-snapshots</id>
			<name>Spring Snapshots</name>
			<url>https://repo.spring.io/snapshot</url>
			<releases>
				<enabled>false</enabled>
			</releases>
		</repository>
		<repository>
			<name>Central Portal Snapshots</name>
			<id>central-portal-snapshots</id>
			<url>https://central.sonatype.com/repository/maven-snapshots/</url>
			<releases>
				<enabled>false</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
	</repositories>
</project>