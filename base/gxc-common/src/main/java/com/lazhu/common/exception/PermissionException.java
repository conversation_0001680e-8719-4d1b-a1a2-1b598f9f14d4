package com.lazhu.common.exception;

/**
 * 权限异常
 */
public class PermissionException extends RuntimeException {

    private String resource;
    private String action;

    public PermissionException(String message) {
        super(message);
    }

    public PermissionException(String resource, String action, String message) {
        super(message);
        this.resource = resource;
        this.action = action;
    }

    public PermissionException(String message, Throwable cause) {
        super(message, cause);
    }

    public PermissionException(String resource, String action, String message, Throwable cause) {
        super(message, cause);
        this.resource = resource;
        this.action = action;
    }

    public String getResource() {
        return resource;
    }

    public String getAction() {
        return action;
    }
}
