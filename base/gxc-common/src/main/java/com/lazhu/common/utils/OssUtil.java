package com.lazhu.common.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.aliyun.oss.OSS;
import com.lazhu.common.enums.UploadFileTypeEnums;
import com.sun.istack.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;

@Slf4j
@Component
public class OssUtil {


    @Autowired
    private OSS oss;

    @Value("${lz.upload.ossBucket:}")
    private String bucketName;

    @Value("${lz.upload.ossBaseKey:}")
    private String ossBaseKey;

    @Value("${lz.upload.ossWebSite:}")
    private String ossWebSite;

    /**
     * 文件上传
     *
     * @param file 文件
     * @return 上传地址
     */
    public String upload(File file) {
        String path = buildDefaultPath(file);
        try {
            oss.putObject(bucketName, path, FileUtil.getInputStream(file));
            return ossWebSite + "/" + path;
        } catch (Exception e) {
            log.error("oss文件上传失败,file:{}", file.getAbsolutePath(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 文件上传(指定路径)
     *
     * @param file 文件
     * @param dir  指定路径
     * @return 上传地址
     */
    public String upload(File file, String dir) {
        String basePath = getBasePath();
        dir = dir.endsWith("/") ? dir : dir + "/";
        String path = basePath + dir + file.getName();
        try {
            oss.putObject(bucketName, path, FileUtil.getInputStream(file));
            return ossWebSite + "/" + path;
        } catch (Exception e) {
            log.error("oss文件上传失败,file:{},dir:{}", file.getAbsolutePath(), dir, e);
            throw new RuntimeException(e);
        }
    }


    public String upload(String url, String filePath) {
        String basePath = getBasePath();
        String path = basePath + filePath;
        try (InputStream inputStream = URLUtil.getStream(URLUtil.url(url))) {
            oss.putObject(bucketName, path, inputStream);
            return ossWebSite + "/" + path;
        } catch (IOException e) {
            log.error("oss文件上传失败,url:{}", url, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 构建默认上传路径
     *
     * @param file 文件
     * @return 默认路径
     */
    private String buildDefaultPath(File file) {
        String fileExt = FileUtil.getSuffix(file.getName());
        String fileType = UploadFileTypeEnums.getType(FileUtil.getSuffix(file.getName())).getName();
        String date = DateUtil.format(new Date(), "yyyy/MM/dd");
        String timeStamp = System.currentTimeMillis() + RandomUtil.randomNumbers(6);
        String path = getBasePath();
        return path + fileType + "/" + date + "/" + timeStamp + (StrUtil.isNotBlank(fileExt) ? "." + fileExt : "");
    }

    @NotNull
    private String getBasePath() {
        return StrUtil.isNotBlank(ossBaseKey) ? ossBaseKey + "/" : "";
    }

}
