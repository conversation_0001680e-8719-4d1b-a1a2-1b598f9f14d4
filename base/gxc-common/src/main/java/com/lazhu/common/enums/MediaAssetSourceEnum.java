package com.lazhu.common.enums;

import lombok.Getter;

import java.io.Serializable;
import java.util.Arrays;

/**
 * 素材来源枚举
 */
@Getter
public enum MediaAssetSourceEnum implements Serializable {
    /**
     * 系统素材
     */
    SYSTEM("1", "系统素材"),

    /**
     * 用户上传素材
     */
    USER("2", "用户上传素材");

    private final String source;
    private final String desc;

    MediaAssetSourceEnum(String source, String desc) {
        this.source = source;
        this.desc = desc;
    }

    /**
     * 根据source 获取枚举
     */
    public static MediaAssetSourceEnum getMediaAssetSource(String source) {
        return Arrays.stream(MediaAssetSourceEnum.values())
                .filter(mediaAssetSource -> mediaAssetSource.getSource().equals(source))
                .findFirst()
                .orElse(null);
    }
}