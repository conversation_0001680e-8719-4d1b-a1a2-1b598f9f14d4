package com.lazhu.common.enums;

import java.io.Serializable;
import java.util.Arrays;

/**
 * 大模型类型枚举
 */
public enum ModelTypeEnum implements Serializable {
    
    /** 文本类型 */
    TEXT(1, "文本"),
    
    /** 图像类型 */
    IMAGE(2, "图像"),
    
    /** 音频类型 */
    AUDIO(3, "音频"),
    
    /** 视频类型 */
    VIDEO(4, "视频");

    private final Integer type;
    private final String desc;

    ModelTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据类型值获取枚举实例
     * @param type 类型值
     * @return 枚举实例，如果未找到返回null
     */
    public static ModelTypeEnum fromType(Integer type) {
        return Arrays.stream(values())
                     .filter(modelType -> modelType.getType().equals(type))
                     .findFirst()
                     .orElse(null);
    }
}
