package com.lazhu.common.enums;

import java.io.Serializable;

/**
 * 内容类型枚举
 */
public enum ContentTypeEnum implements Serializable {
    /**
     * 图文类型
     */
   TXT("1", "图文"),
    
    /**
     * 视频类型
     */
    VIDEO("2", "视频");

    private final String type;
    private final String desc;

    ContentTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}