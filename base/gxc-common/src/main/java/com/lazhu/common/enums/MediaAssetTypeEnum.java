package com.lazhu.common.enums;

import lombok.Getter;

import java.io.Serializable;
import java.util.Arrays;

/**
 * 素材类型枚举
 */
@Getter
public enum MediaAssetTypeEnum implements Serializable {
    /**
     * 图片类型
     */
    IMAGE("1", "图片"),

    /**
     * 视频类型
     */
    VIDEO("2", "视频"),

    /**
     * 音频类型
     */
    AUDIO("3", "音频");

    private final String type;
    private final String desc;

    MediaAssetTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 根据type 获取枚举
     */
    public static MediaAssetTypeEnum getMediaAssetType(String type) {
        return Arrays.stream(MediaAssetTypeEnum.values()).filter(mediaAssetType -> mediaAssetType.getType().equals(type)).findFirst().orElse(null);
    }
}