package com.lazhu.common.utils;

import com.lazhu.secure.Decoder;
import com.lazhu.secure.Encoder;
import com.lazhu.secure.Model;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 手机号加密解密
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-23
 */
public class KmsSecretUtil {

    private static final int MODEL = Model.DEV;

    public final static Decoder decoder;

    public final static Encoder encode;

    static {
        decoder = new Decoder(MODEL);
        encode = new Encoder(MODEL);
    }

    /**
     * @param
     * @return
     * @author: hezx
     * @description: 编码手机号
     * @date 2020/11/19 9:18
     */
    public static String encrypt(String data) {
        if (StringUtils.isEmpty(data)) {
            return null;
        }
        try {
            return encode.encode(data);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * @param
     * @return
     * @author: hezx
     * @description: 解码手机号
     * @date 2020/11/19 9:17
     */
    public static String decrypt(String data) {
        if (StringUtils.isEmpty(data)) {
            return null;
        }
        try {
            int realModel = getRealModel(data);
            if (realModel != MODEL) {
                return new Decoder(realModel).decode(data);
            } else {
                return decoder.decode(data);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static int getRealModel(String data) {
        if (StringUtils.isBlank(data)) {
            return Model.DEV;
        }
        return data.startsWith("DEV.") ? Model.DEV : Model.PRO;
    }

}
