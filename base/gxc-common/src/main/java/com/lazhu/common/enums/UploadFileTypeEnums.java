package com.lazhu.common.enums;

import cn.hutool.core.util.StrUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public enum UploadFileTypeEnums {

	@SuppressWarnings("serial")
	IMG("image", new ArrayList<String>() {
		{
			String[] is = { "png", "jpg", "jpeg", "gif", "bmp" };
			addAll(Arrays.asList(is));
		}
	}),

	@SuppressWarnings("serial")
	AUDIO("audio", new ArrayList<String>() {
		{
			String[] as = { "mp3", "wma", "amr", "mid", "wav" };
			addAll(Arrays.asList(as));
		}
	}),

	@SuppressWarnings("serial")
	VIDEO("video", new ArrayList<String>() {
		{
			String[] vs = { "flv", "swf", "mkv", "avi", "rm", "rmvb", "mpeg", "mpg", "ogg", "ogv", "mov", "wmv", "mp4",
					"webm" };
			addAll(Arrays.asList(vs));
		}
	}),

	@SuppressWarnings("serial")
	DOC("doc", new ArrayList<String>() {
		{
			String[] ds = { "doc", "docx", "xls", "xlsx", "pdf", "pptx", "potx", "pps", "txt" };
			addAll(Arrays.asList(ds));
		}
	}),

	@SuppressWarnings("serial")
	OTHER("dmo", new ArrayList<String>() {
		{

		}
	}),

	@SuppressWarnings("serial")
	DISABLE("disa", new ArrayList<String>() {
		{
			String[] ds = { "js", "html", "py", "shell", "web", "sh", "exe" };
			addAll(Arrays.asList(ds));
		}
	});

	private String name;

	private List<String> fileSuffix;

	private UploadFileTypeEnums(String name, List<String> fileSuffix) {
		this.name = name;
		this.fileSuffix = fileSuffix;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public List<String> getFileSuffix() {
		return fileSuffix;
	}

	public void setFileSuffix(List<String> fileSuffix) {
		this.fileSuffix = fileSuffix;
	}

	public static UploadFileTypeEnums getType(String fileExt) {
		if (StrUtil.isBlank(fileExt)) {
			return OTHER;
		}
		if (IMG.getFileSuffix().contains(fileExt)) {
			return IMG;
		}
		if (AUDIO.getFileSuffix().contains(fileExt)) {
			return AUDIO;
		}
		if (VIDEO.getFileSuffix().contains(fileExt)) {
			return VIDEO;
		}
		if (DOC.getFileSuffix().contains(fileExt)) {
			return DOC;
		}
		return OTHER;
	}

}
