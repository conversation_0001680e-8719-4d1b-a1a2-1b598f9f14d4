package com.lazhu.common.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;

import java.io.*;
import java.util.ArrayList;
import java.util.List;


@Slf4j
public class MediaUtil {

    /**
     * ffmpeg.exe 路径
     */
    private static String FFMPEG_PATH;

    static {
        try {
            File ffmpegFile = getFFmpegBinary();
            FFMPEG_PATH = ffmpegFile.getAbsolutePath();
        } catch (Exception e) {
            log.error("获取ffmpeg路径失败", e);
            throw new RuntimeException(e);
        }
        log.info("获取ffmpeg路径 >> {}", FFMPEG_PATH);
    }

    /**
     * 根据视频/音频时长（秒）
     */
    public static Long getDuration(String url) {
        log.info("获取视频/音频时长 >> url:{}", url);
        try {
            Process process = Runtime.getRuntime().exec(new String[]{FFMPEG_PATH, "-i", url});
            BufferedReader reader = new BufferedReader(
                    new InputStreamReader(process.getErrorStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.contains("Duration")) {
                    String[] parts = line.split(",");
                    String durationPart = parts[0].replace("Duration:", "").trim();
                    String[] timeParts = durationPart.split(":");
                    long hours = Long.parseLong(timeParts[0]);
                    long minutes = Long.parseLong(timeParts[1]);
                    double secondsWithMillis = Double.parseDouble(timeParts[2]);
                    return (hours * 3600) + (minutes * 60) + (long) secondsWithMillis;
                }
            }
        } catch (Exception e) {
            log.error("获取视频/音频时长失败 >> url:{}", url, e);
        }
        return 0L; // Default if duration could not be parsed
    }


    /**
     * 获取视频第一帧图片
     */
    public static File getVideoCover(String url) {
        log.info("获取视频第一帧图片 >> url:{}", url);

        try {
            // 使用系统临时目录+随机文件名
            String tempDir = System.getProperty("java.io.tmpdir");
            String fileName = "video_cover_" + FileUtil.getPrefix(url) + ".jpg";
            File file = new File(tempDir, fileName);

            // 清理已存在的文件
            if (file.exists()) {
                FileUtil.del(file);
            }

            // 简化FFmpeg命令（移除不必要的转义和引号）
            String[] args = new String[]{
                    FFMPEG_PATH,
                    "-i", url,
                    "-ss", "00:00:00",  // 定位到起始位置
                    "-vframes", "1",    // 只取一帧
                    "-q:v", "2",        // 输出质量
                    "-y",               // 覆盖输出文件
                    file.getAbsolutePath()
            };

            log.info("执行ffmpeg命令:{}", String.join(" ", args));

            // 使用ProcessBuilder替代Runtime.exec
            Process process = new ProcessBuilder(args)
                    .redirectErrorStream(true)
                    .start();

            // 等待命令完成并检查退出码
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                throw new IOException("FFmpeg process exited with code " + exitCode);
            }

            // 验证文件是否生成
            if (!file.exists() || file.length() == 0) {
                throw new IOException("Generated cover file is empty or not created");
            }

            return file;
        } catch (Exception e) {
            log.error("获取视频第一帧图片失败 >> url:{}", url, e);
            return null;
        }
    }


    /**
     * 媒体截取
     *
     * @param url   视频地址
     * @param start 开始时间 （秒）
     * @param end   截取结束时间 （秒）
     * @return 截取后的地址
     */
    public static String cut(String url, long start, long end) {
        log.info("截取媒体文件 >> url:{}, start:{}, end:{}", url, start, end);

        // 获取文件名和扩展名
        String fileName = FileUtil.getName(url);
        String fileNameWithoutExt = FileUtil.getPrefix(fileName);
        String extName = FileUtil.getSuffix(fileName);

        // 创建临时文件
        File tempFile = FileUtil.createTempFile(fileNameWithoutExt + "_cut_" + start + "_" + end, "." + extName, null, true);
        if (tempFile.exists()) {
            tempFile.delete();
        }

        try {
            // 构建ffmpeg命令
            // -ss 参数指定开始时间，-t 参数指定持续时间，-c copy 参数指定复制编解码器（不重新编码，提高速度）
            String[] args = new String[]{
                    FFMPEG_PATH,
                    "-ss", start + "",
                    "-i", url,
                    "-t", end + "",
                    "-c", "copy",
                    tempFile.getAbsolutePath()
            };

            log.info("执行ffmpeg命令:{}", StrUtil.join(" ", args));

            // 执行命令
            Process process = Runtime.getRuntime().exec(args);

            // 获取错误输出
            BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
            String line;
            StringBuilder errorOutput = new StringBuilder();
            while ((line = errorReader.readLine()) != null) {
                errorOutput.append(line).append("\n");
            }

            // 等待命令执行完成
            int exitCode = process.waitFor();

            // 检查命令是否成功执行
            if (exitCode != 0) {
                log.error("截取媒体文件失败 >> url:{}, 错误信息:{}", url, errorOutput.toString());
                return null;
            }

            // 返回临时文件的绝对路径
            log.info("截取媒体文件成功 >> url:{}, 输出文件:{}", url, tempFile.getAbsolutePath());
            return tempFile.getAbsolutePath();
        } catch (Exception e) {
            log.error("截取媒体文件异常 >> url:{}", url, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 视频合成
     *
     * @param urls    输入视频文件路径列表（支持本地文件路径和网络URL）
     * @param outPath 输出文件路径
     * @param urls    输入视频文件路径列表
     * @param outPath 输出文件名
     */
    public static void merge(List<String> urls, String outPath) {
        log.info("视频合成 >> 输入文件:{}", StrUtil.join(", ", urls));

        if (urls == null || urls.isEmpty()) {
            throw new IllegalArgumentException("输入视频列表不能为空");
        }

        if (urls.size() == 1) {
            // 只有一个视频，直接复制或下载
            String sourceUrl = urls.get(0);
            try {
                if (sourceUrl.startsWith("http://") || sourceUrl.startsWith("https://")) {
                    // 网络URL，下载文件
                    HttpUtil.downloadFile(sourceUrl, outPath);
                } else {
                    // 本地文件，复制文件
                    FileUtil.copy(sourceUrl, outPath, true);
                }
                log.info("只有一个视频，直接复制到输出路径 >> 输出文件:{}", outPath);
                return;
            } catch (Exception e) {
                log.error("复制单个视频文件失败 >> 错误信息:{}", e.getMessage());
                throw new RuntimeException("复制单个视频文件失败", e);
            }
        }

        // 多个视频需要合并，先下载网络视频到本地
        List<String> localVideoPaths = new ArrayList<>();
        List<File> tempFiles = new ArrayList<>();

        try {
            for (String url : urls) {
                if (url.startsWith("http://") || url.startsWith("https://")) {
                    // 下载网络视频到临时文件
                    File tempFile = FileUtil.createTempFile("video_merge_" + System.currentTimeMillis(), ".mp4", null, true);
                    HttpUtil.downloadFile(url, tempFile);
                    localVideoPaths.add(tempFile.getAbsolutePath());
                    tempFiles.add(tempFile);
                    log.info("下载网络视频到临时文件 >> URL:{}, 临时文件:{}", url, tempFile.getAbsolutePath());
                } else {
                    // 本地文件路径
                    localVideoPaths.add(url);
                }
            }

            // 创建列表文件
            File listFile = FileUtil.createTempFile("listfile_" + System.currentTimeMillis(), ".txt", null, false);
            tempFiles.add(listFile); // 添加到临时文件列表以便清理
            try (BufferedWriter writer = new BufferedWriter(new FileWriter(listFile))) {
                for (String path : localVideoPaths) {
                    writer.write("file '" + path.replace("'", "'\\''") + "'");
                    writer.newLine();
                }
            } catch (IOException e) {
                log.error("创建列表文件失败 >> 错误信息:{}", e.getMessage());
                throw new RuntimeException("创建列表文件失败", e);
            }

            // 尝试使用快速合并（仅复制流）
            if (tryFastMerge(listFile.getAbsolutePath(), outPath)) {
                log.info("视频合成成功 >> 输出文件:{}", outPath);
            }

        } finally {
            // 清理临时文件
            for (File tempFile : tempFiles) {
                if (tempFile.exists()) {
                    FileUtil.del(tempFile);
                }
            }
        }
    }

    /**
     * 尝试快速合并（仅复制流，要求编码格式一致）
     */
    private static boolean tryFastMerge(String listFilePath, String outPath) {
        try {
            File outputDir = new File(outPath).getParentFile();
            if (!outputDir.exists()) {
                outputDir.mkdirs();
            }
            // 构建ffmpeg命令
            String[] args = new String[]{
                    FFMPEG_PATH,
                    "-f", "concat",
                    "-safe", "0",
                    "-i", listFilePath,
                    "-c", "copy",
                    outPath,
                    "-y" // 覆盖输出文件
            };

            log.info("执行快速视频合并:{}", StrUtil.join(" ", args));

            // 执行命令
            Process process = new ProcessBuilder(args).redirectErrorStream(true).start();

            // 获取输出
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }

            // 等待命令执行完成
            int exitCode = process.waitFor();

            // 检查命令是否成功执行
            if (exitCode != 0) {
                log.warn("快速视频合并失败 >> 错误信息:{}", output.toString());
                return false;
            }

            return true;
        } catch (Exception e) {
            log.warn("快速视频合并异常 >> 错误信息:{}", e.getMessage());
            return false;
        }
    }

    /**
     * 添加字幕到视频
     *
     * @param videoUrl     视频路径
     * @param subtitlePath 字幕文件路径
     * @param outPath      输出文件路径
     */
    public static void addSubtitle(String videoUrl, String subtitlePath, String outPath) {
        log.info("添加字幕到视频 >> 视频路径:{}, 字幕文件:{}", videoUrl, subtitlePath);
        subtitlePath = subtitlePath.replace("\\", "/");
        String osName = System.getProperty("os.name").toLowerCase();
        // 判断操作系统类型,替换路径
        if (osName.contains("win")) {
            subtitlePath = "C\\:" + subtitlePath.substring(2);
        }
        try {
            // 构建ffmpeg命令，-i 参数指定输入文件，-vf 参数用于添加滤镜（这里是添加字幕），-c copy 参数复制编解码器
            String[] args = new String[]{
                    FFMPEG_PATH,
                    "-y",
                    "-i", videoUrl,
                    "-vf", "subtitles='" + subtitlePath + "'",
                    outPath
            };

            log.info("执行ffmpeg命令:{}", StrUtil.join(" ", args));

            // 执行命令
            Process process = Runtime.getRuntime().exec(args);

            // 获取错误输出
            BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
            String line;
            StringBuilder errorOutput = new StringBuilder();
            while ((line = errorReader.readLine()) != null) {
                errorOutput.append(line).append("\n");
            }

            // 等待命令执行完成
            int exitCode = process.waitFor();

            // 检查命令是否成功执行
            if (exitCode != 0) {
                log.error("添加字幕失败 >> 视频路径:{}, 字幕文件:{}, 错误信息:{}", videoUrl, subtitlePath, errorOutput.toString());
                throw new RuntimeException("添加字幕失败");
            }

            // 返回临时文件的绝对路径
            log.info("添加字幕成功 >> 视频路径:{}, 输出文件:{}", videoUrl, outPath);
        } catch (Exception e) {
            log.error("添加字幕异常 >> 视频路径:{}, 字幕文件:{}", videoUrl, subtitlePath, e);
            throw new RuntimeException("添加字幕异常", e);
        }
    }


    private static final String ExecutablePath = "/data";

    /**
     * 获取ffmpeg
     */
    private static File getFFmpegBinary() throws Exception {
        // 根据操作系统确定文件后缀
        String suffix = isWindows() ? ".exe" : "";

        // 创建临时文件
        File ffmpegFile = FileUtil.file(FileUtil.getTmpDirPath() + "/ffmpeg" + suffix);
        if (ffmpegFile.exists()) {
            return ffmpegFile;
        }
        ffmpegFile = FileUtil.touch(FileUtil.getTmpDirPath() + "/ffmpeg" + suffix);


        // 从classpath资源加载ffmpeg
        String fileName = isWindows() ? "ffmpeg.exe" : "ffmpeg";
        ClassPathResource resource = new ClassPathResource(fileName);
        try (InputStream in = resource.getInputStream();
             FileOutputStream out = new FileOutputStream(ffmpegFile)) {
            // 复制资源到临时文件
            StreamUtils.copy(in, out);
        }

        // 非Windows系统设置可执行权限
        if (!isWindows()) {
            if (ffmpegFile.setExecutable(true)) {
                log.info("设置ffmpeg可执行权限成功");
            } else {
                throw new RuntimeException("无法设置ffmpeg可执行权限");
            }
        }
        return ffmpegFile;
    }

    private static boolean isWindows() {
        return System.getProperty("os.name").toLowerCase().contains("win");
    }

    public static void main(String[] args) {
//        String videoUrl = "http://dashscope-result-sh.oss-cn-shanghai.aliyuncs.com/1d/ba/20250624/30eefb8a/1d1f2dbe-13f3-4521-908c-5cc53998dd4e_vidretalk.mp4?Expires=1750839816&OSSAccessKeyId=LTAI5tKPD3TMqf2Lna1fASuh&Signature=5Nb4dmnaC%2BYl4BKsky8Z%2BfHCLdQ%3D";
//        HttpUtil.downloadFile(videoUrl, "C:\\Users\\<USER>\\AppData\\Local\\Temp\\voice\\1935678324591476737\\tt.mp4");
        String videoPath = "C:\\Users\\<USER>\\AppData\\Local\\Temp\\content_combine\\1935678324591476737\\temp_combine.mp4";
        Long duration = getDuration(videoPath);
        System.out.println(duration);
        System.out.println(getVideoCover(videoPath));
        String subtitlePath = "C:\\Users\\<USER>\\AppData\\Local\\Temp\\voice\\1935678324591476737\\audio.srt";
//        addSubtitle(videoPath, subtitlePath);
//        String path = URLUtil.getPath(videoUrl);
//        System.out.println(path);
    }


}
