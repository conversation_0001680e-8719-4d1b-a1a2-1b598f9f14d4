package com.lazhu.gxc.publish.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.lazhu.gxc.publish.common.ChannelType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class UserChannelAccountDTO implements Serializable {

    /**
     * 主键ID
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    /**
     * 用户ID
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long userId;

    /**
     * 渠道类型  {@link ChannelType}
     */
    private Integer channel;

    /**
     * 账号名称
     */
    private String account;

    /**
     * 登录状态 0下线  1在线
     */
    private Integer loginStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 登录Cookie
     */
    @JSONField(serialize = false)
    private String cookie;

}
