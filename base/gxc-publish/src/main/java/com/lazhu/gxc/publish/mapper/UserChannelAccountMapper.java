package com.lazhu.gxc.publish.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.gxc.publish.entity.UserChannelAccount;
import com.lazhu.gxc.publish.entity.UserChannelAccountDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface UserChannelAccountMapper extends BaseMapper<UserChannelAccount> {

    List<UserChannelAccountDTO> selectIdPage(Page<UserChannelAccountDTO> page, @Param("params") Map<String, Object> params);

}
