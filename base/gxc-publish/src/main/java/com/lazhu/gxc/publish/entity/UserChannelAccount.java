package com.lazhu.gxc.publish.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.lazhu.gxc.publish.common.ChannelType;
import lombok.Data;

import java.util.Date;

@Data
@TableName("u_user_channel_account")
public class UserChannelAccount {

    /** 主键ID */
    private Long id;
    
    /** 用户ID */
    private Long userId;
    
    /** 渠道类型  {@link ChannelType} */
    private Integer channel;
    
    /** 账号名称 */
    private String account;
    
    /** 登录Cookie信息 */
    private String cookie;

    /** 登录状态 0下线  1在线 */
    private Integer loginStatus;
    
    /** 创建时间 */
    private Date createTime;
    
    /** 更新时间 */
    private Date updateTime;

}
