package com.lazhu.gxc.publish.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lazhu.gxc.publish.common.ChannelType;
import com.lazhu.gxc.publish.entity.UserChannelAccount;
import com.lazhu.gxc.publish.entity.UserChannelAccountDTO;
import com.lazhu.gxc.publish.entity.UserChannelAccountQuery;
import com.lazhu.gxc.publish.login.ChannelLoginHandler;
import com.lazhu.gxc.publish.login.WxVideoChannelLoginHandler;
import com.lazhu.gxc.publish.mapper.UserChannelAccountMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class UserChannelAccountService extends ServiceImpl<UserChannelAccountMapper, UserChannelAccount> {


    @Autowired
    private UserChannelAccountMapper userChannelAccountMapper;

    /**
     * 登录
     *
     * @param userId  用户ID
     * @param channel 登录平台
     * @param account 登录账号
     */
    public void login(Long userId, ChannelType channel, String account) {
        ChannelLoginHandler service = getChannelLoginService(channel);
        String cookies = service.login(account);
        UserChannelAccount userChannelAccount = queryUserChannelAccount(userId, channel, account);
        if (userChannelAccount == null) {
            userChannelAccount = new UserChannelAccount();
            userChannelAccount.setChannel(channel.getType());
            userChannelAccount.setAccount(account);
            userChannelAccount.setCookie(cookies);
            userChannelAccount.setUserId(userId);
            userChannelAccount.setLoginStatus(1);
            userChannelAccount.setCreateTime(new Date());
            userChannelAccountMapper.insert(userChannelAccount);
        } else {
            userChannelAccount.setLoginStatus(1);
            userChannelAccount.setCookie(cookies);
            userChannelAccount.setUpdateTime(new Date());
            userChannelAccountMapper.updateById(userChannelAccount);
        }
    }

    public UserChannelAccount queryUserChannelAccount(Long userId, ChannelType channel, String account) {
        List<UserChannelAccount> userChannelAccounts = list(new LambdaQueryWrapper<UserChannelAccount>()
                .eq(UserChannelAccount::getUserId, userId)
                .eq(UserChannelAccount::getChannel, channel.getType())
                .eq(StrUtil.isNotBlank( account),UserChannelAccount::getAccount, account));
        return !userChannelAccounts.isEmpty() ? userChannelAccounts.getFirst() : null;
    }

    public Page<UserChannelAccountDTO> queryPage(UserChannelAccountQuery param) {
        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null) {
            param.setPageSize(10);
        }
        Page<UserChannelAccountDTO> page = new Page<>(param.getPageNo(), param.getPageSize());
        List<UserChannelAccountDTO> list = userChannelAccountMapper.selectIdPage(page, BeanUtil.beanToMap(param));
        page.setRecords(list);
        return page;
    }

    public boolean checkLogin(String cookie, ChannelType channel) {
        ChannelLoginHandler service = getChannelLoginService(channel);
        return service.checkLogin(cookie);
    }

    private static ChannelLoginHandler getChannelLoginService(ChannelType channel) {
        if (Objects.requireNonNull(channel) == ChannelType.wx_video) {
            return SpringUtil.getBean(WxVideoChannelLoginHandler.class);
        } else {
            throw new RuntimeException("不支持的渠道类型");
        }
    }


}
