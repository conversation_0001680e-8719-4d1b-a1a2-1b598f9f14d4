package com.lazhu.gxc.publish.login;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.microsoft.playwright.*;
import com.microsoft.playwright.options.Cookie;
import com.microsoft.playwright.options.LoadState;
import com.microsoft.playwright.options.SameSiteAttribute;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 微信视频号登录服务
 */
@Service
@Slf4j
public class WxVideoChannelLoginHandler implements ChannelLoginHandler {

    /**
     * 微信视频号登录页面
     */
    private static final String LOGIN_URL = "https://channels.weixin.qq.com/login.html";
    /**
     * 登录成功后的首页
     */
    private static final String INDEX_URL = "https://channels.weixin.qq.com/platform";

    @Override
    public String login(String account) {
        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch(new BrowserType.LaunchOptions().setHeadless(false));
            Page page = browser.newContext().newPage();
            // 访问登录页面
            page.navigate(LOGIN_URL);
            page.waitForLoadState(LoadState.DOMCONTENTLOADED);
            // 等待二维码出现
            page.waitForSelector("iframe");
            log.info("{} 视频号登录，等待扫码 ... ", account);
            //等待扫码
            page.waitForURL(url -> url.contains("channels.weixin.qq.com") && !url.contains("login"));
            log.info("{} 视频号登录，登录成功", account);
            //保存cookie
            List<Cookie> cookies = page.context().cookies();
            //转化为字符串 返回登录后的凭证 cookie
            return JSONObject.toJSONString(cookies);
        }
    }

    /**
     * 检查是否已登录
     */
    public boolean checkLogin(String cookieStr) {
        if (StrUtil.isEmpty(cookieStr)) {
            return false;
        }
        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch();
            BrowserContext browserContext = browser.newContext();
            List<Cookie> cookies = new ArrayList<>();
            JSONArray objects = JSONObject.parseArray(cookieStr);
            for (Object object : objects) {
                JSONObject jsonObject = (JSONObject) object;
                String name = jsonObject.getString("name");
                String value = jsonObject.getString("value");
                Cookie cookie = new Cookie(name, value);
                cookie.domain = jsonObject.getString("domain");
                cookie.path = jsonObject.getString("path");
                cookie.expires = jsonObject.getDouble("expires");
                cookie.httpOnly = jsonObject.getBoolean("httpOnly");
                cookie.secure = jsonObject.getBoolean("secure");
                cookie.sameSite = SameSiteAttribute.valueOf(jsonObject.getString("sameSite"));
                cookies.add(cookie);
            }
            browserContext.addCookies(cookies);
            Page page = browserContext.newPage();
            page.navigate(INDEX_URL);
            // 等待页面加载
            page.waitForLoadState(LoadState.DOMCONTENTLOADED);
            // 检查是否重定向到登录页面
            if (page.url().contains("login")) {
                return false;
            }
            // 检查是否存在用户信息或发布按钮等登录后的元素
            page.waitForSelector(".account-info .name", new Page.WaitForSelectorOptions().setTimeout(120000));
            return true;
        } catch (Exception e) {
            log.error("{} 登录失效", cookieStr, e);
            return false;
        }
    }
}