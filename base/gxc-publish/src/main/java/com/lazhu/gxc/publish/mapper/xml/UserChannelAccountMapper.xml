<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lazhu.gxc.publish.mapper.UserChannelAccountMapper">

    <select id="selectIdPage" resultType="com.lazhu.gxc.publish.entity.UserChannelAccountDTO">
        select * from u_user_channel_account
        <where>
            <if test="params.userId != null and params.userId != ''">
                and user_id = #{params.userId}
            </if>
            <if test="params.channel != null">
                and channel = #{params.channel}
            </if>
            <if test="params.account != null and params.account != ''">
                and account like concat(concat('%',#{params.account}),'%')
            </if>
            <if test="params.loginStatus != null">
                and login_status = #{params.loginStatus}
            </if>
        </where>
    </select>
</mapper>
