package com.lazhu.system.sysparams.service;

import com.lazhu.support.base.BaseService;
import com.lazhu.system.sysparams.entity.SysParams;
import com.lazhu.system.sysparams.mapper.SysParamsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-27
 */
@Service
public class SysParamsService extends BaseService<SysParams> {

	@Autowired
	SysParamsMapper mapper;

//	@Autowired
//	RedissonClient redisson;

	public String queryByKey(String key) {
//		String pkey = SysConst.PARAM_CACHE_KEY + key;
//		RBucket<String> bucket = redisson.getBucket(pkey);
		String value = null;
//		if (bucket.get() == null) {
			SysParams sp = mapper.queryByKey(key);
//			if (sp != null) {
//				bucket.set(sp.getParamValue());
				value = sp.getParamValue();
//			}
//		} else {
//			value = bucket.get();
//		}
		return value;

	}

}
