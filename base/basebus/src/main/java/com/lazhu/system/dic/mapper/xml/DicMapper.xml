<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lazhu.system.dic.mapper.DicMapper">

	<select id="selectIdPage"
		resultType="com.lazhu.system.dic.entity.Dic">
		select * from sys_dic
		<where>
			<if test="params.code != null and params.code != ''">
				and code = #{params.code}
			</if>
			<if test="params.orderNum != null">
				and order_num = #{params.orderNum}
			</if>
			<if test="params.value != null and params.value != ''">
				and value = #{params.value}
			</if>
			<if test="params.txt != null and params.txt != ''">
				and txt like concat(concat('%',#{params.txt}),'%')
			</if>
		</where>
		order by code asc , order_num asc
	</select>

	<select id="queryByCodes"
		resultType="com.lazhu.system.dic.entity.Dic">
		select * from sys_dic where code in
		<foreach collection="list" item="code" open="(" close=")"
			separator=",">
			#{code}
		</foreach>
	</select>
</mapper>
