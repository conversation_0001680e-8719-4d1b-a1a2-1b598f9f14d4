package com.lazhu.system.dic.service;

import com.lazhu.support.base.BaseService;
import com.lazhu.system.dic.entity.Dic;


import com.lazhu.system.dic.mapper.DicMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-27
 */
@Service
public class DicService extends BaseService<Dic> {

	@Autowired
	DicMapper mapper;

	public List<Dic> queryByCodes(List<String> codes) {
		return mapper.queryByCodes(codes);
	}

}
