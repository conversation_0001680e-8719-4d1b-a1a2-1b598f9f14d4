package com.lazhu.system.dic.mapper;

import com.lazhu.support.base.BaseMapper;
import com.lazhu.system.dic.entity.Dic;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-27
 */
@Repository
public interface DicMapper extends BaseMapper<Dic> {

	List<Dic> queryByCodes(List<String> codes);

}
