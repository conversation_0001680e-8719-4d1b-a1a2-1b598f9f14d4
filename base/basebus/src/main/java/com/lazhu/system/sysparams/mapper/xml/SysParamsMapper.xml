<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.lazhu.system.sysparams.mapper.SysParamsMapper">

	<select id="selectIdPage"
		resultType="com.lazhu.system.sysparams.entity.SysParams">
		select * from sys_params
		<where>
			<if test="params.paramKey != null and params.paramKey != ''">
				and param_key = #{params.paramKey}
			</if>
			<if test="params.paramValue != null and params.paramValue != ''">
				and param_value like
				concat(concat('%',#{params.paramValue}),'%')
			</if>
		</where>
	</select>

	<select id="queryByKey"
		resultType="com.lazhu.system.sysparams.entity.SysParams">
		select * from sys_params where param_key = #{key} limit 1
	</select>
</mapper>
