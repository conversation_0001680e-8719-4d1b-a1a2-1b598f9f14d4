package com.lazhu.system.dic.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lazhu.support.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-27
 */
@TableName("sys_dic")
@Data
@EqualsAndHashCode(callSuper = true)
public class Dic extends BaseModel {
	private static final long serialVersionUID = 1L;
	// code
	@TableField("code")
	private String code;
	// order_num
	@TableField("order_num")
	private Integer orderNum;
	// value
	@TableField("value")
	private String value;
	// txt
	@TableField("txt")
	private String txt;
}
