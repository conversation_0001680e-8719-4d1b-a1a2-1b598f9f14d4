package com.lazhu.system.sysparams.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lazhu.support.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-27
 */
@TableName("sys_params")
@Data
@EqualsAndHashCode(callSuper = true)
public class SysParams extends BaseModel {
	private static final long serialVersionUID = 1L;
	// param_key
	@TableField("param_key")
	private String paramKey;
	// param_value
	@TableField("param_value")
	private String paramValue;

}
