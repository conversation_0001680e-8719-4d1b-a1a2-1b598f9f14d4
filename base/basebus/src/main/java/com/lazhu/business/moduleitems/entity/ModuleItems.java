package com.lazhu.business.moduleitems.entity;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.lazhu.support.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 模块元素配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@TableName("cfg_module_items")
@Data
@EqualsAndHashCode(callSuper = true)
public class ModuleItems extends BaseModel {
    private static final long serialVersionUID = 1L;
    //module_code 模块编码 
    @TableField("module_code")
    private String moduleCode;
    //item_name 元素名称 
    @TableField("item_name")
    private String itemName;
    //image_url 图片 
    @TableField("image_url")
    private String imageUrl;
    //link_url 跳转 
    @TableField("link_url")
    private String linkUrl;
    //order_num 排序;数值小的在前面 
    @TableField("order_num")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer orderNum;
    
}
