package com.lazhu.business.content.dto;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.io.Serial;
import java.util.Date;
import java.util.List;

@Data
public class ContentDTO implements java.io.Serializable{

    @Serial
    private static final long serialVersionUID = 1L;

    //id
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    //user_id 所属用户
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long userId;
    //actor_id 演员id
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long actorId;
    //演员姓名
    private String actorName;
    //topic 创作主题
    private String topic;
    //title 标题
    private String title;
    //status 状态 (0:草稿, 1:完成, 2:已发布)
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer status;
    //content_type 类型
    private String contentType;
    //summary 摘要
    private String summary;
    //content 文案内容
    private String content;
    //cover_img 封面图
    private List<String> coverImg;
    //media_url 视频链接
    private String mediaUrl;
    //视频时长 （毫秒）
    private Long duration;
    // 创建时间
    private Date createTime;
    // 更新时间
    private Date updateTime;
    // 元数据
    private JSONObject metaData;
    private String exposeMediaUrl;
}
