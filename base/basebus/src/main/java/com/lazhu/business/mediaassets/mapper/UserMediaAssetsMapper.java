package com.lazhu.business.mediaassets.mapper;

import com.lazhu.business.mediaassets.entity.UserMediaAssets;
import com.lazhu.support.base.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 * 用户上传素材表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
public interface UserMediaAssetsMapper extends BaseMapper<UserMediaAssets> {

    Long count(@Param("params") Map<String, Object> param);
}
