package com.lazhu.business.llmconfig.service;

import cn.hutool.core.collection.CollUtil;
import com.lazhu.business.llmconfig.mapper.LlmConfigMapper;
import com.lazhu.common.enums.ModelTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lazhu.support.base.BaseService;
import com.lazhu.business.llmconfig.entity.LlmConfig;

import java.util.Map;

/**
 * <p>
 * 大模型配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Service
public class LlmConfigService extends BaseService<LlmConfig> {

    @Autowired
    private LlmConfigMapper llmConfigMapper;

    /**
     * 查询当前使用的语音模型
     */
    public LlmConfig queryCurrentVoiceLLM() {
        return queryCurrentLLM(ModelTypeEnum.AUDIO);
    }

    /**
     * 获取当前使用的文本模型
     */
    public LlmConfig queryCurrentTextLLM() {
        return queryCurrentLLM(ModelTypeEnum.TEXT);
    }

    /**
     * 获取当前使用的图像模型
     */
    public LlmConfig queryCurrentImageLLM() {
        return queryCurrentLLM(ModelTypeEnum.IMAGE);
    }

    /**
     * 获取当前使用的视频模型
     */
    public LlmConfig queryCurrentVideoLLM() {
        return queryCurrentLLM(ModelTypeEnum.VIDEO);
    }


    /**
     * 根据语音模型类型查询当前使用的模型
     */
    public LlmConfig queryCurrentLLM(ModelTypeEnum modelType) {
        return llmConfigMapper.queryCurrentLLM(modelType.getType());
    }

    /**
     * 根据供应商和模型类型查询当前使用的模型
     */
    public LlmConfig queryByProviderAndType(String provider, ModelTypeEnum modelType) {
        Map<String, Object> param = Map.of("modelSupplier", provider, "modelType", modelType.getType(), "useStatus", 1);
        return CollUtil.getFirst(queryList(param));
    }


}
