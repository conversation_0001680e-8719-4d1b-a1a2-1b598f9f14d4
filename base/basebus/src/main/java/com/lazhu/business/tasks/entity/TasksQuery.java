package com.lazhu.business.tasks.entity;

import java.util.Date;

import com.lazhu.support.base.BaseQuery;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 任务记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TasksQuery extends BaseQuery {
    //content_id 内容id 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long contentId;
    //type 类型;1 文本创作 2文本修改 3视频对口型 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer type;
    //task_id 任务id 
    private String taskId;
    //status 任务状态;0 未开始 1进行中 2成功 3失败 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer status;
    //result 任务结果 
    private String result;

    //排除id
    private Long excludeId;

}
