package com.lazhu.business.user.entity;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.lazhu.support.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@TableName("u_user")
@Data
@EqualsAndHashCode(callSuper = true)
public class User extends BaseModel {
    private static final long serialVersionUID = 1L;
    //nick_name 昵称 
    @TableField("nick_name")
    private String nickName;
    //head_img 头像 
    @TableField("head_img")
    private String headImg;
    //mobile 手机号kms加密 
    @TableField("mobile")
    private String mobile;
    //mobile_md5 手机号md5 
    @TableField("mobile_md5")
    private String mobileMd5;
    //user_status 状态（1 启用 0禁用） 
    @TableField("user_status")
    private String userStatus;
    
}
