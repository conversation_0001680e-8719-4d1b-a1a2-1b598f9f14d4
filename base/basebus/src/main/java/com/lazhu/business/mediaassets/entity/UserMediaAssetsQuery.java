package com.lazhu.business.mediaassets.entity;

import com.lazhu.support.base.BaseQuery;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 用户上传素材表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserMediaAssetsQuery extends BaseQuery {
    //assert_type 素材类型 
    private String assetsType;
    //title 描述 
    private String title;
    //media_url 链接 
    private String mediaUrl;
    //duration_ms 时长 (毫秒) 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long durationMs;
    //actor_id 演员id 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long actorId;

}
