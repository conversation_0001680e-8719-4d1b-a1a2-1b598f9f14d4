package com.lazhu.business.mediaassets.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.lazhu.support.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <p>
 * 素材分类表
 * </p>
 * @since 2025-07-28
 */
@TableName("c_media_assets_category")
@Data
@EqualsAndHashCode(callSuper = true)
public class MediaAssetsCategory extends BaseModel {
    @Serial
    private static final long serialVersionUID = 1L;
    
    //category_name 分类名称
    @TableField("category_name")
    private String categoryName;
    
    //parent_id 父分类ID，0表示顶级分类
    @TableField("parent_id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long parentId;
    
    //type 适用素材类型：1-图片 2-视频 3-音频 0-通用
    @TableField("type")
    private String type;
    
    //sort_order 排序字段
    @TableField("sort_order")
    private Integer sortOrder;
    
    //description 分类描述
    @TableField("description")
    private String description;
    
    //status 状态：1-启用 0-禁用
    @TableField("status")
    private Integer status;
}