package com.lazhu.business.mediaassets.mapper;

import com.lazhu.business.mediaassets.entity.MediaAssetsVoice;
import com.lazhu.support.base.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 * 系统声音素材表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
public interface MediaAssetsVoiceMapper extends BaseMapper<MediaAssetsVoice> {

    Long count(@Param("params") Map<String, Object> params);
}
