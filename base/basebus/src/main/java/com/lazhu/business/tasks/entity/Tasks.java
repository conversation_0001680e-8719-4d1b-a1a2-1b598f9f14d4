package com.lazhu.business.tasks.entity;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.lazhu.support.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 任务记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@TableName("t_tasks")
@Data
@EqualsAndHashCode(callSuper = true)
public class Tasks extends BaseModel {
    private static final long serialVersionUID = 1L;
    //content_id 内容id 
    @TableField("content_id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long contentId;
    //type 类型;1 文本创作 2文本修改 3视频对口型 
    @TableField("type")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer type;
    //task_id 任务id 
    @TableField("task_id")
    private String taskId;
    //任务优先级
    @TableField("priority")
    private Integer priority;
    //status 任务状态;0 未开始 1进行中 2成功 3失败 
    @TableField("status")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer status;
    //result 任务结果 
    @TableField("result")
    private String result;
    
}
