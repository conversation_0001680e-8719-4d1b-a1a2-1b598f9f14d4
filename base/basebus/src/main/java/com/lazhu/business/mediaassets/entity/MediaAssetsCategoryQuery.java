package com.lazhu.business.mediaassets.entity;

import com.lazhu.support.base.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 素材分类查询实体
 * </p>
 * @since 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MediaAssetsCategoryQuery extends BaseQuery {
    
    //分类名称（模糊查询）
    private String categoryName;
    
    //父分类ID
    private Long parentId;
    
    //适用素材类型
    private String type;
    
    //状态
    private Integer status;
}