package com.lazhu.business.mediaassets.entity;

import com.lazhu.support.base.BaseQuery;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 系统图片素材表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MediaAssetsImgQuery extends BaseQuery {
    //title 描述 
    private String title;
    //img_url 素材链接 
    private String imgUrl;
    //actor_id 演员id;为空则为通用素材 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long actorId;

}
