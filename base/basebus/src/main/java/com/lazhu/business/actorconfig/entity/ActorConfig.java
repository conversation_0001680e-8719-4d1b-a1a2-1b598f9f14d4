package com.lazhu.business.actorconfig.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lazhu.common.enums.ContentTypeEnum;
import com.lazhu.support.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 人物配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@TableName("c_actor_config")
@Data
@EqualsAndHashCode(callSuper = true)
public class ActorConfig extends BaseModel {
    private static final long serialVersionUID = 1L;
    //character_type 人设类型;1：人设背景   2：人设文风  3：人设性格 4：掌握技能 
    @TableField("character_type")
    private String characterType;
    //character_name 人设名称 
    @TableField("character_name")
    private String characterName;
    //character_prompt 人设提示词 
    @TableField("character_prompt")
    private String characterPrompt;
    // 平台 字典：PLAT_CODE
    @TableField("plat")
    private String plat;
    //source 数据来源 1 系统 2 用户
    @TableField("source")
    private String source;
    // 所属用户
    @TableField("user_id")
    private Long userId;
}
