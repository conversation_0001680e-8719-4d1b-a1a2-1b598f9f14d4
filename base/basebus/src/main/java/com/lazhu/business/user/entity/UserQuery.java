package com.lazhu.business.user.entity;

import java.util.Date;

import com.lazhu.support.base.BaseQuery;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserQuery extends BaseQuery {
    //nick_name 昵称 
    private String nickName;
    //head_img 头像 
    private String headImg;
    //mobile 手机号
    private String mobile;
    //mobile_md5 手机号md5 
    private String mobileMd5;
    //user_status 状态（1 启用 0禁用） 
    private String userStatus;

}
