package com.lazhu.business.actor.entity;

import com.lazhu.support.base.BaseQuery;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 演员表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ActorQuery extends BaseQuery {
    //nick_name 昵称 
    private String nickName;
    //head_img 头像 
    private String headImg;
    //tags 标签 
    private String tags;
    //profile 简介 
    private String profile;
    //role_prompt 人设提示词 
    private String rolePrompt;
    //use_status 状态(1 启用 0禁用) 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer useStatus;

    /**
     * 只查询自己创建的角色 1 是 0否
     */
    private String onlyMine;
    private Long createBy;

}
