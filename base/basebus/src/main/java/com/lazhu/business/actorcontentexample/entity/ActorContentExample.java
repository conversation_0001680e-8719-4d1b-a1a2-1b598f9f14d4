package com.lazhu.business.actorcontentexample.entity;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.lazhu.support.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 角色文章案例
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@TableName("c_actor_content_example")
@Data
@EqualsAndHashCode(callSuper = true)
public class ActorContentExample extends BaseModel {
    private static final long serialVersionUID = 1L;
    //actor_id 角色id 
    @TableField("actor_id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long actorId;
    //plat 平台 
    @TableField("plat")
    private String plat;
    //title 标题 
    @TableField("title")
    private String title;
    //content 内容 
    @TableField("content")
    private String content;
    
}
