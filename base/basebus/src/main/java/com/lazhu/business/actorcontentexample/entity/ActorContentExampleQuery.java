package com.lazhu.business.actorcontentexample.entity;

import java.util.Date;

import com.lazhu.support.base.BaseQuery;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 角色文章案例
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ActorContentExampleQuery extends BaseQuery {
    //actor_id 角色id 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long actorId;
    //plat 平台 
    private String plat;
    //title 标题 
    private String title;
    //content 内容 
    private String content;

}
