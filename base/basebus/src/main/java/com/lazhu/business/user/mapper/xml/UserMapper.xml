<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lazhu.business.user.mapper.UserMapper">

	<select id="selectIdPage" resultType="com.lazhu.business.user.entity.User">
		select * from u_user
		<where>
			<if test="params.nickName != null and params.nickName != ''">
			    and nick_name like concat(concat('%',#{params.nickName}),'%')
			</if>
			<if test="params.headImg != null and params.headImg != ''">
			    and head_img like concat(concat('%',#{params.headImg}),'%')
			</if>
			<if test="params.mobile != null and params.mobile != ''">
			    and mobile like concat(concat('%',#{params.mobile}),'%')
			</if>
			<if test="params.mobileMd5 != null and params.mobileMd5 != ''">
			    and mobile_md5 like concat(concat('%',#{params.mobileMd5}),'%')
			</if>
			<if test="params.userStatus != null and params.userStatus != ''">
			    and user_status like concat(concat('%',#{params.userStatus}),'%')
			</if>
		</where>
	</select>
    <select id="queryByMobile" resultType="com.lazhu.business.user.entity.User">
		select * from u_user where mobile_md5 = #{mobile}
	</select>
</mapper>
