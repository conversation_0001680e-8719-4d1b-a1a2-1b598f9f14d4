package com.lazhu.business.user.service;

import java.util.Date;

import com.lazhu.business.user.mapper.UserMapper;
import com.lazhu.common.utils.ComplexMD5Util;
import com.lazhu.common.utils.KmsSecretUtil;
import com.lazhu.support.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lazhu.support.base.BaseService;
import com.lazhu.business.user.entity.User;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Service
public class UserService extends BaseService<User> {

    @Autowired
    private UserMapper userMapper;

    public User queryByMobile(String mobile) {
        String md5Mobile = ComplexMD5Util.MD5Encode(mobile);
        return userMapper.queryByMobile(md5Mobile);
    }

    public User createUser(String mobile) {
        User user = new User();
        user.setNickName(StringUtils.fuzzMobile(mobile));
        user.setMobile(KmsSecretUtil.encrypt(mobile));
        user.setMobileMd5(ComplexMD5Util.MD5Encode(mobile));
        user.setUserStatus("1");
        user.setCreateTime(new Date());
        user.setUpdateTime(new Date());
        userMapper.insert(user);
        return user;
    }
}
