package com.lazhu.business.actor.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.lazhu.support.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 演员表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@TableName("c_actor")
@Data
@EqualsAndHashCode(callSuper = true)
public class Actor extends BaseModel {
    private static final long serialVersionUID = 1L;
    //nick_name 昵称 
    @TableField("nick_name")
    private String nickName;
    //head_img 头像 
    @TableField("head_img")
    private String headImg;
    //profile 简介 
    @TableField("profile")
    private String profile;
    //role_prompt 人设提示词 
    @TableField("role_prompt")
    private String rolePrompt;
    //use_status 状态(1 启用 0禁用) 
    @TableField("use_status")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer useStatus;

    /**
     * 创建人
     */
    @TableField("create_by")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long createBy;
    
}
