package com.lazhu.business.mediaassets.strategy.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.business.mediaassets.dto.MediaAssetsDTO;
import com.lazhu.business.mediaassets.dto.MediaAssetsQuery;
import com.lazhu.business.mediaassets.entity.MediaAssetsVideo;
import com.lazhu.business.mediaassets.service.MediaAssetsVideoService;
import com.lazhu.business.mediaassets.strategy.MediaAssetStrategy;
import com.lazhu.common.enums.MediaAssetSourceEnum;
import com.lazhu.common.utils.MediaUtil;
import com.lazhu.common.utils.OssUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 视频素材策略实现
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Component
public class VideoAssetStrategy implements MediaAssetStrategy {

    @Autowired
    private MediaAssetsVideoService mediaAssetsVideoService;

    @Autowired
    private OssUtil ossUtil;

    @Override
    public Page<MediaAssetsDTO> queryAssets(MediaAssetsQuery query) {
        // 执行查询
        Page<MediaAssetsVideo> videoPage = mediaAssetsVideoService.queryPage(BeanUtil.beanToMap(query));

        // 转换为统一素材对象
        List<MediaAssetsDTO> unifiedAssets = videoPage.getRecords().stream().map(video ->
                new MediaAssetsDTO(
                        video.getId(),
                        "2", // 视频类型
                        MediaAssetSourceEnum.SYSTEM.getSource(),
                        video.getTitle(),
                        video.getVideoUrl(),
                        video.getDurationMs(),
                        video.getCoverImg(),
                        video.getCreateTime()
                )
        ).collect(Collectors.toList());

        // 创建返回的分页对象
        Page<MediaAssetsDTO> resultPage = new Page<>(videoPage.getCurrent(), videoPage.getSize());
        resultPage.setRecords(unifiedAssets);
        resultPage.setTotal(videoPage.getTotal());

        return resultPage;
    }

    @Override
    public Long countAssets(MediaAssetsQuery query) {
        return mediaAssetsVideoService.count(BeanUtil.beanToMap(query));
    }


    @Override
    public MediaAssetsDTO uploadAsset(MediaAssetsDTO param) {
        MediaAssetsVideo video = new MediaAssetsVideo();
        video.setTitle(param.getTitle());
        String fileUrl = param.getUrl();
        video.setVideoUrl(fileUrl);
        Long duration = MediaUtil.getDuration(fileUrl);
        video.setDurationMs(duration);
        File coverImgFile = MediaUtil.getVideoCover(fileUrl);
        String coverImgUrl = ossUtil.upload(coverImgFile);
        video.setCoverImg(coverImgUrl);
        FileUtil.del(coverImgFile);

        mediaAssetsVideoService.save(video);
        return new MediaAssetsDTO(
                video.getId(),
                "2", // 视频类型
                MediaAssetSourceEnum.SYSTEM.getSource(),
                video.getTitle(),
                video.getVideoUrl(),
                video.getDurationMs(),
                coverImgUrl,
                video.getCreateTime()
        );
    }

    @Override
    public Boolean updateAsset(Long id, String title) {
        MediaAssetsVideo video = new MediaAssetsVideo();
        video.setId(id);
        video.setTitle(title);
        mediaAssetsVideoService.update(video);
        return true;
    }

    @Override
    public Boolean batchDeleteAssets(List<Long> ids) {
        return mediaAssetsVideoService.batchDelById(ids) > 0;
    }

}