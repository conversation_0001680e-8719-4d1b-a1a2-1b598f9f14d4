package com.lazhu.business.mediaassets.service;

import com.lazhu.business.mediaassets.mapper.MediaAssetsVideoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lazhu.support.base.BaseService;
import com.lazhu.business.mediaassets.entity.MediaAssetsVideo;

import java.util.Map;

/**
 * <p>
 * 系统视频素材表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Service
public class MediaAssetsVideoService extends BaseService<MediaAssetsVideo> {


    @Autowired
    private MediaAssetsVideoMapper mapper;

    public Long count(Map<String, Object> params) {
        return mapper.count(params);
    }
}
