<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lazhu.business.actorconfig.mapper.ActorConfigMapper">

    <select id="selectIdPage" resultType="com.lazhu.business.actorconfig.entity.ActorConfig">
        select * from c_actor_config
        <where>
            <if test="params.characterType != null and params.characterType != ''">
                and character_type like concat(concat('%',#{params.characterType}),'%')
            </if>
            <if test="params.characterName != null and params.characterName != ''">
                and character_name like concat(concat('%',#{params.characterName}),'%')
            </if>
            <if test="params.source != null and params.source != ''">
                and source = #{params.source}
            </if>
            <if test="params.userId != null and params.userId != ''">
                <choose>
                    <when test="params.onlyMine == '1'">
                        <!--只查询自己创建的-->
                        and user_id = #{params.userId}
                    </when>
                    <otherwise>
                        <!-- 查询所有（自己+系统内置）-->
                        and ( user_id = #{params.userId} or user_id is null )
                    </otherwise>
                </choose>
            </if>
            <if test="params.plat != null and params.plat != ''">
                and plat = #{params.plat}
            </if>
        </where>
    </select>
</mapper>
