package com.lazhu.business.mediaassets.service;

import com.lazhu.business.mediaassets.mapper.MediaAssetsVoiceMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lazhu.support.base.BaseService;
import com.lazhu.business.mediaassets.entity.MediaAssetsVoice;

import java.util.Map;

/**
 * <p>
 * 系统声音素材表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Service
public class MediaAssetsVoiceService extends BaseService<MediaAssetsVoice> {

    @Autowired
    private MediaAssetsVoiceMapper mapper;


    public Long count(Map<String, Object> params) {
        return mapper.count(params);
    }
}
