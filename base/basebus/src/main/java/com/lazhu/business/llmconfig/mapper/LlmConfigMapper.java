package com.lazhu.business.llmconfig.mapper;

import com.lazhu.business.llmconfig.entity.LlmConfig;
import com.lazhu.support.base.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 大模型配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Repository
public interface LlmConfigMapper extends BaseMapper<LlmConfig> {

    /**
     * 根据模型类型查询当前模型
     * @param type 模型类型
     */
    LlmConfig queryCurrentLLM(@Param("type") Integer type);
}
