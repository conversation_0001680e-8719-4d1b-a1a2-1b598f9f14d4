package com.lazhu.business.mediaassets.service;


import com.lazhu.business.mediaassets.entity.UserMediaAssets;
import com.lazhu.business.mediaassets.mapper.UserMediaAssetsMapper;
import com.lazhu.support.base.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <p>
 * 用户上传素材表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Service
public class UserMediaAssetsService extends BaseService<UserMediaAssets> {


    @Autowired
    private UserMediaAssetsMapper userMediaAssetsMapper;

    public Long count(Map<String, Object> param) {
        return userMediaAssetsMapper.count(param);
    }
}
