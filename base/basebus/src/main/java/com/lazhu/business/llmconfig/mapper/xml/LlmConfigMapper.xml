<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lazhu.business.llmconfig.mapper.LlmConfigMapper">

	<select id="selectIdPage" resultType="com.lazhu.business.llmconfig.entity.LlmConfig">
		select * from llm_config
		<where>
			<if test="params.modelType != null and params.modelType != ''">
			    and model_type like concat(concat('%',#{params.modelType}),'%')
			</if>
			<if test="params.modelId != null and params.modelId != ''">
			    and model_id like concat(concat('%',#{params.modelId}),'%')
			</if>
			<if test="params.modelSupplier != null and params.modelSupplier != ''">
			    and model_supplier like concat(concat('%',#{params.modelSupplier}),'%')
			</if>
			<if test="params.apiKey != null and params.apiKey != ''">
			    and api_key like concat(concat('%',#{params.apiKey}),'%')
			</if>
			<if test="params.useStatus != null and params.useStatus != ''">
				and use_status = #{params.useStatus}
			</if>
		</where>
	</select>
    <select id="queryCurrentLLM" resultType="com.lazhu.business.llmconfig.entity.LlmConfig">
		select * from llm_config where model_type = #{type} and use_status = 1
	</select>
</mapper>
