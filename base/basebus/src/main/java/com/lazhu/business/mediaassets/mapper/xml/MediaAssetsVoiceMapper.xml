<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lazhu.business.mediaassets.mapper.MediaAssetsVoiceMapper">


    <sql id="base_query">
        <where>
            <if test="params.title != null and params.title != ''">
                and title like concat(concat('%',#{params.title}),'%')
            </if>
            <if test="params.assertIds != null and params.assertIds.size > 0">
                and id in
                <foreach item="item" collection="params.assertIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </where>
    </sql>

    <select id="selectIdPage" resultType="com.lazhu.business.mediaassets.entity.MediaAssetsVoice">
        select * from c_media_assets_voice
        <include refid="base_query"/>
    </select>

    <select id="count" resultType="java.lang.Long">
        select count(1) from c_media_assets_voice
        <include refid="base_query"/>
    </select>
</mapper>
