package com.lazhu.business.actor.service;

import cn.hutool.core.collection.CollUtil;
import com.lazhu.business.actor.mapper.ActorMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lazhu.support.base.BaseService;
import com.lazhu.business.actor.entity.Actor;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 演员表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Service
public class ActorService extends BaseService<Actor> {

    @Autowired
    private ActorMapper actorMapper;

    public List<Actor> selectByIds(List<Long> ids){
        if(CollUtil.isEmpty(ids)){
            return Collections.emptyList();
        }
        return actorMapper.selectByIds(ids);
    }

}
