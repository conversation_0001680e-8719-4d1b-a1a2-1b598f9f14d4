package com.lazhu.business.llmconfig.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lazhu.support.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 大模型配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@TableName("llm_config")
@Data
@EqualsAndHashCode(callSuper = true)
public class LlmConfig extends BaseModel {
    private static final long serialVersionUID = 1L;
    //model_type 模型类型  如文本、图像、音视频等
    @TableField("model_type")
    private String modelType;
    //model_id 模型id 
    @TableField("model_id")
    private String modelId;
    //model_supplier 模型厂商 
    @TableField("model_supplier")
    private String modelSupplier;
    // 接口地址
    @TableField("api_url")
    private String apiUrl;
    //api_key API密钥
    @TableField("api_key")
    private String apiKey;
    //其他参数 (json)
    @TableField("ext_params")
    private String extParams;

}
