package com.lazhu.business.mediaassets.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.lazhu.support.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <p>
 * 素材分类关联表
 * </p>
 *
 * @since 2025-07-28
 */
@TableName("c_media_assets_category_join")
@Data
@EqualsAndHashCode(callSuper = true)
public class MediaAssetsCategoryJoin extends BaseModel {
    @Serial
    private static final long serialVersionUID = 1L;

    //asset_id 素材ID
    @TableField("asset_id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long assetId;

    //asset_type 素材类型：1-图片 2-视频 3-音频
    @TableField("asset_type")
    private String assetType;

    //category_id 分类ID
    @TableField("category_id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long categoryId;

    /**
     * 素材来源 1-系统 2-用户
     */
    @TableField("source")
    private Integer source;
}