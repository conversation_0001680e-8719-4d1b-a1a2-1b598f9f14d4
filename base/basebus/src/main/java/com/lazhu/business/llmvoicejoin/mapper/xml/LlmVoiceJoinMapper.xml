<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lazhu.business.llmvoicejoin.mapper.LlmVoiceJoinMapper">

    <select id="selectIdPage" resultType="com.lazhu.business.llmvoicejoin.entity.LlmVoiceJoin">
		select * from llm_voice_join
		<where>
			<if test="params.assetsType != null">
				and asserts_type = #{params.assetsType}
			</if>
			<if test="params.voiceAssertsId != null">
				and voice_asserts_id = #{params.voiceAssertsId}
			</if>
			<if test="params.voiceId != null and params.voiceId != ''">
			    and voice_id like concat(concat('%',#{params.voiceId}),'%')
			</if>
			<if test="params.llmId != null">
				and llm_id = #{params.llmId}
			</if>
		</where>
	</select>
    <select id="queryVoiceId" resultType="com.lazhu.business.llmvoicejoin.entity.LlmVoiceJoin">
		select * from llm_voice_join where assets_type = #{source} and llm_id = #{llmId} and voice_assets_id = #{audioId}
	</select>

	<delete id="deleteByAudioId">
		delete from llm_voice_join where voice_assets_id = #{audioId} and assets_type = #{source}
	</delete>
</mapper>
