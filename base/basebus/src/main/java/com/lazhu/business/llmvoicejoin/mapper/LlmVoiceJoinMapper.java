package com.lazhu.business.llmvoicejoin.mapper;

import com.lazhu.business.llmvoicejoin.entity.LlmVoiceJoin;
import com.lazhu.support.base.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 大模型音色素材关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Repository
public interface LlmVoiceJoinMapper extends BaseMapper<LlmVoiceJoin> {

    /**
     * 根据模型id和素材id查询音色id
     * @param source 素材来源
     * @param llmId 模型id
     * @param audioId 素材id
     */
    LlmVoiceJoin queryVoiceId(@Param("source") Integer source,@Param("llmId") Long llmId, @Param("audioId") Long audioId);

    void deleteByAudioId(@Param("source") Integer source,@Param("audioId") Long audioId);
}
