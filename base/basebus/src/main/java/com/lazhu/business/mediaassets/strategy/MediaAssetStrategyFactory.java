package com.lazhu.business.mediaassets.strategy;

import com.lazhu.business.mediaassets.strategy.impl.AudioAssetStrategy;
import com.lazhu.business.mediaassets.strategy.impl.ImageAssetStrategy;
import com.lazhu.business.mediaassets.strategy.impl.VideoAssetStrategy;
import com.lazhu.common.enums.MediaAssetTypeEnum;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 媒体素材策略工厂
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Component
public class MediaAssetStrategyFactory {

    @Autowired
    private ImageAssetStrategy imageAssetStrategy;

    @Autowired
    private VideoAssetStrategy videoAssetStrategy;

    @Autowired
    private AudioAssetStrategy audioAssetStrategy;

    private final Map<String, MediaAssetStrategy> strategyMap = new HashMap<>();

    @PostConstruct
    public void init() {
        strategyMap.put(MediaAssetTypeEnum.IMAGE.getType(), imageAssetStrategy);
        strategyMap.put(MediaAssetTypeEnum.VIDEO.getType(), videoAssetStrategy);
        strategyMap.put(MediaAssetTypeEnum.AUDIO.getType(), audioAssetStrategy);
    }

    /**
     * 根据素材类型获取对应的策略
     *
     * @param assetType 素材类型
     * @return 素材策略
     */
    public MediaAssetStrategy getStrategy(String assetType) {
        MediaAssetStrategy strategy = strategyMap.get(assetType);
        if (strategy == null) {
            throw new IllegalArgumentException("不支持的素材类型: " + assetType);
        }
        return strategy;
    }

    /**
     * 获取所有策略
     *
     * @return 所有策略列表
     */
    public List<MediaAssetStrategy> getAllStrategies() {
        return List.copyOf(strategyMap.values());
    }
}