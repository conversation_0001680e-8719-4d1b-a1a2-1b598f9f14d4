package com.lazhu.business.llmconfig.entity;

import java.util.Date;

import com.lazhu.support.base.BaseQuery;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 大模型配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LlmConfigQuery extends BaseQuery {
    //model_type 模型类型 
    private String modelType;
    //model_id 模型id 
    private String modelId;
    //model_supplier 模型厂商 
    private String modelSupplier;
    //api_key API密钥 
    private String apiKey;

}
