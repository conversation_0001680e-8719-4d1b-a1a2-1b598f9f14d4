<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lazhu.business.moduleitems.mapper.ModuleItemsMapper">

	<select id="selectIdPage" resultType="com.lazhu.business.moduleitems.entity.ModuleItems">
		select * from cfg_module_items
		<where>
			<if test="params.moduleCode != null and params.moduleCode != ''">
			    and module_code like concat(concat('%',#{params.moduleCode}),'%')
			</if>
			<if test="params.itemName != null and params.itemName != ''">
			    and item_name like concat(concat('%',#{params.itemName}),'%')
			</if>
			<if test="params.imageUrl != null and params.imageUrl != ''">
			    and image_url like concat(concat('%',#{params.imageUrl}),'%')
			</if>
			<if test="params.linkUrl != null and params.linkUrl != ''">
			    and link_url like concat(concat('%',#{params.linkUrl}),'%')
			</if>
			<if test="params.orderNum != null">
				and order_num = #{params.orderNum}
			</if>
		</where>
		order by order_num
	</select>
</mapper>
