package com.lazhu.business.actorconfig.entity;

import com.lazhu.support.base.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 人物配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ActorConfigQuery extends BaseQuery {
    //character_type 人设类型;1：人设背景   2：人设文风  3：人设性格 4：掌握技能 
    private String characterType;
    //character_name 人设名称 
    private String characterName;
    //character_prompt 人设提示词 
    private String characterPrompt;
    /**
     * 来源 1 平台  2  用户
     */
    private String source;

    /**
     * 所属用户
     */
    private Long userId;

    /**
     * 所属平台 字典：PLAT_CODE
     */
    private String plat;

    /**
     * 是否只看自己创建的  1是 0否
     */
    private String onlyMine;

}
