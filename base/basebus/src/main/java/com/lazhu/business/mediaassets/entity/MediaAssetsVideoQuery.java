package com.lazhu.business.mediaassets.entity;

import com.lazhu.support.base.BaseQuery;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 系统视频素材表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MediaAssetsVideoQuery extends BaseQuery {
    //title 描述 
    private String title;
    //video_url 视频链接 
    private String videoUrl;
    //duration_ms 时长 (毫秒) 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long durationMs;
    //actor_id 演员id 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long actorId;

}
