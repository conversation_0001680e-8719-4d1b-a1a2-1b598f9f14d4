package com.lazhu.business.mediaassets.mapper;

import com.lazhu.business.mediaassets.entity.MediaAssetsCategoryJoin;
import com.lazhu.support.base.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 素材分类关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Mapper
public interface MediaAssetsCategoryRelMapper extends BaseMapper<MediaAssetsCategoryJoin> {

    /**
     * 根据分类ID查询关联素材
     */
    List<MediaAssetsCategoryJoin> queryByCategory(@Param("categoryId") Long categoryId, @Param("assetType") String assetType, @Param("source") Integer source);

    /**
     * 删除素材的所有分类关联
     */
    int deleteByAsset(@Param("assetIds") List<Long> assetIds, @Param("assetType") String assetType, @Param("source") Integer source);

}