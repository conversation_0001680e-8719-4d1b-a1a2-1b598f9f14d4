package com.lazhu.business.mediaassets.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 素材列表dto
 */
@Data
public class MediaAssetsDTO implements Serializable {

    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    private String assetsType;

    /**
     * 素材来源 1-系统素材 2-用户上传素材
     */
    private String source;

    private String title;

    private String url;

    /**
     * 封面
     */
    private String coverImg;

    /**
     * 时长
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long duration;

    /**
     * 角色id
     */
    @JSONField(serializeUsing =  ToStringSerializer.class)
    private Long actorId;

    /**
     * 角色名称
     */
    private String actorName;


    private Date createTime;

    private Long createBy;

    public MediaAssetsDTO() {

    }

    public MediaAssetsDTO(Long id, String type,String source, String title, String url, Long durationMs,
                          String coverImg, Date createTime) {
        this(id, type, source, title, url, durationMs, coverImg, createTime,null,null);
    }

    public MediaAssetsDTO(Long id, String type, String source, String title,
                          String url, Long durationMs,
                          String coverImg, Date createTime, Long actorId,String actorName) {
        this.id = id;
        this.assetsType = type;
        this.source = source;
        this.title = title;
        this.url = url;
        this.duration = durationMs;
        this.coverImg = coverImg;
        this.createTime = createTime;
        this.actorId = actorId;
        this.actorName = actorName;
    }
}
