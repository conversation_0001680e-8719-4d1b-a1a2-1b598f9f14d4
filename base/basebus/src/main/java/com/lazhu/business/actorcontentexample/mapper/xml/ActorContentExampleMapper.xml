<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lazhu.business.actorcontentexample.mapper.ActorContentExampleMapper">

	<select id="selectIdPage" resultType="com.lazhu.business.actorcontentexample.entity.ActorContentExample">
		select * from c_actor_content_example
		<where>
			<if test="params.actorId != null">
				and actor_id = #{params.actorId}
			</if>
			<if test="params.plat != null and params.plat != ''">
			    and plat like concat(concat('%',#{params.plat}),'%')
			</if>
			<if test="params.title != null and params.title != ''">
			    and title like concat(concat('%',#{params.title}),'%')
			</if>
			<if test="params.content != null and params.content != ''">
			    and content like concat(concat('%',#{params.content}),'%')
			</if>
		</where>
	</select>
</mapper>
