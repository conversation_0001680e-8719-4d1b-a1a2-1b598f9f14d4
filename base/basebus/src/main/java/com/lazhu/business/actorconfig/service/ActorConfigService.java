package com.lazhu.business.actorconfig.service;

import cn.hutool.core.collection.CollectionUtil;
import org.springframework.stereotype.Service;

import com.lazhu.support.base.BaseService;
import com.lazhu.business.actorconfig.entity.ActorConfig;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;

/**
 * <p>
 * 人物配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Service
public class ActorConfigService extends BaseService<ActorConfig> {


    /**
     * 根据ids批量查询
     */
    public List<ActorConfig> queryByIds(List<Long> ids) {
        if(CollectionUtil.isEmpty(ids)){
            return Collections.emptyList();
        }
        return mapper.selectByIds(ids);
    }

}
