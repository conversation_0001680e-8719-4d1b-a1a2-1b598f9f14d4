package com.lazhu.business.moduleitems.entity;

import java.util.Date;

import com.lazhu.support.base.BaseQuery;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * <p>
 * 模块元素配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ModuleItemsQuery extends BaseQuery {
    //module_code 模块编码 
    private String moduleCode;
    //item_name 元素名称 
    private String itemName;
    //image_url 图片 
    private String imageUrl;
    //link_url 跳转 
    private String linkUrl;
    //order_num 排序;数值小的在前面 
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer orderNum;

}
