<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lazhu.business.actor.mapper.ActorMapper">

    <select id="selectIdPage" resultType="com.lazhu.business.actor.entity.Actor">
        select * from c_actor
        <where>
            <if test="params.nickName != null and params.nickName != ''">
                and nick_name like concat(concat('%',#{params.nickName}),'%')
            </if>
            <if test="params.profile != null and params.profile != ''">
                and profile like concat(concat('%',#{params.profile}),'%')
            </if>
            <if test="params.rolePrompt != null and params.rolePrompt != ''">
                and role_prompt like concat(concat('%',#{params.rolePrompt}),'%')
            </if>
            <if test="params.createBy != null">
                <if test="params.onlyMine == '1'">
                    <!--只查询自己创建的演员-->
                    and create_by = #{params.createBy}
                </if>
                <!-- 查询所有（自己+系统内置）演员-->
                <if test="params.onlyMine != '1'">
                    and ( create_by = #{params.createBy} or create_by is null )
                </if>
            </if>
            <if test="params.useStatus != null">
                and use_status = #{params.useStatus}
            </if>
        </where>
    </select>
</mapper>
