package com.lazhu.business.mediaassets.strategy;

import com.lazhu.business.mediaassets.strategy.impl.SystemAssetSourceStrategy;
import com.lazhu.business.mediaassets.strategy.impl.UserAssetSourceStrategy;
import com.lazhu.common.enums.MediaAssetSourceEnum;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 媒体素材来源策略工厂
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Component
public class MediaAssetSourceStrategyFactory {

    @Autowired
    private SystemAssetSourceStrategy systemAssetSourceStrategy;

    @Autowired
    private UserAssetSourceStrategy userAssetSourceStrategy;

    private final Map<String, MediaAssetSourceStrategy> sourceStrategyMap = new HashMap<>();

    @PostConstruct
    public void init() {
        sourceStrategyMap.put(MediaAssetSourceEnum.SYSTEM.getSource(), systemAssetSourceStrategy);
        sourceStrategyMap.put(MediaAssetSourceEnum.USER.getSource(), userAssetSourceStrategy);
    }

    /**
     * 根据素材来源获取对应的策略
     *
     * @param source 素材来源
     * @return 素材来源策略
     */
    public MediaAssetSourceStrategy getSourceStrategy(String source) {
        MediaAssetSourceStrategy strategy = sourceStrategyMap.get(source);
        if (strategy == null) {
            throw new IllegalArgumentException("不支持的素材来源: " + source);
        }
        return strategy;
    }

    /**
     * 获取所有来源策略
     *
     * @return 所有来源策略列表
     */
    public List<MediaAssetSourceStrategy> getAllSourceStrategies() {
        return List.copyOf(sourceStrategyMap.values());
    }
}