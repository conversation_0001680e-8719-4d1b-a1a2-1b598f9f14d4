package com.lazhu.business.content.dto;

import lombok.Data;

import java.util.List;

/**
 * 内容创作入参
 */
public class ContentCreationReq  {
    /**
     * 文本创作入参
     */
    @Data
    public static class TextRequest {
        /**
         * 内容类型 1 图文  2口播文案
         */
        private String contentType;
        /**
         * 演员id
         */
        private Long actorId;
        /**
         * 主题
         */
        private String topic;
        /**
         * 提示词
         */
        private ContentMetaData metaData;

        /**
         * 行文风格id
         */
        private List<Long> styleIds;
    }

    /**
     * 文本微调入参
     *
     * @param textRequest    创作入参
     * @param conversationId 对话id
     * @param editIndex      修改索引
     * @param query          修改建议
     */
    public record TextFineTuneRequest(
            TextRequest textRequest,
            String conversationId,
            Integer editIndex,
            String query,
            String title,
            String content) {
    }

    /**
     * 视频创作入参
     */
    @Data
    public static class VideoRequest {
        /**
         * 标题文案
         */
        private String title;
        /**
         * 文案
         */
        private String content;
        /**
         * 视频来源
         */
        private Integer videoSource;
        /**
         * 视频id
         */
        private Long videoId;
        /**
         * 音频来源
         */
        private Integer audioSource;
        /**
         * 音频id
         */
        private Long audioId;

        /**
         * 内容
         */
        private ContentDTO contentDTO;

    }
}




