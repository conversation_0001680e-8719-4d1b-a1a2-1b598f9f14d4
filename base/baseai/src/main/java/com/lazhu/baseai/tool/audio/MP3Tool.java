package com.lazhu.baseai.tool.audio;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import cn.hutool.core.io.FileUtil;

public class MP3Tool {

	/**
	 * 合并多个MP3文件
	 * 
	 * @param inputFiles 输入文件路径列表
	 * @param outputFile 输出文件路径
	 * @throws IOException 文件操作异常
	 */
	public static void mergeMp3Files(List<String> inputFiles, String outputFile) throws IOException {
		try (FileOutputStream fos = new FileOutputStream(outputFile);
				BufferedOutputStream bos = new BufferedOutputStream(fos)) {

			for (String inputPath : inputFiles) {
				File file = new File(inputPath);
				if (!file.exists()) {
					System.err.println("文件不存在: " + inputPath);
					continue;
				}

				try (FileInputStream fis = new FileInputStream(file);
						BufferedInputStream bis = new BufferedInputStream(fis)) {

					// 跳过ID3标签（如果存在）
					if (isID3TagPresent(bis)) {
						skipID3Tag(bis);
					}

					// 复制音频数据
					byte[] buffer = new byte[8192];
					int bytesRead;
					while ((bytesRead = bis.read(buffer)) != -1) {
						bos.write(buffer, 0, bytesRead);
					}
				}
			}
		}
	}

	/**
	 * 检查是否包含ID3标签
	 */
	private static boolean isID3TagPresent(BufferedInputStream bis) throws IOException {
		bis.mark(3);
		byte[] header = new byte[3];
		int read = bis.read(header);
		bis.reset();

		// ID3标签以"ID3"开头
		return read == 3 && header[0] == 'I' && header[1] == 'D' && header[2] == '3';
	}

	/**
	 * 跳过ID3标签
	 */
	private static void skipID3Tag(BufferedInputStream bis) throws IOException {
		// 读取标签头
		byte[] header = new byte[10];
		bis.read(header);

		// 计算标签大小（不包括头部的10字节）
		int tagSize = (header[6] & 0x7F) << 21 | (header[7] & 0x7F) << 14 | (header[8] & 0x7F) << 7
				| (header[9] & 0x7F);

		// 跳过标签数据
		long skipped = bis.skip(tagSize);
		if (skipped != tagSize) {
		}
	}

	public static void mergeMp3Files(String firstFilePath, String secondFilePath, String outputFilePath)
			throws IOException {
		File firstFile = new File(firstFilePath);
		File secondFile = new File(secondFilePath);
		File outputFile = new File(outputFilePath);

		try (FileOutputStream fos = new FileOutputStream(outputFile);
				BufferedOutputStream bos = new BufferedOutputStream(fos)) {

			if (!firstFile.exists()) {
				processFile(secondFile, bos, true);
			} else {
				// 处理第一个文件（保留ID3标签）
				processFile(firstFile, bos, true);
				// 处理第二个文件（跳过ID3标签）
				processFile(secondFile, bos, false);
			}
		}
	}

	/**
	 * 处理单个MP3文件
	 * 
	 * @param file    输入文件
	 * @param output  输出流
	 * @param keepId3 是否保留ID3标签
	 * @throws IOException 文件操作异常
	 */
	private static void processFile(File file, BufferedOutputStream output, boolean keepId3) throws IOException {
		try (FileInputStream fis = new FileInputStream(file); BufferedInputStream bis = new BufferedInputStream(fis)) {

			// 检查ID3标签
			boolean hasId3 = isID3TagPresent(bis);

			if (hasId3) {
				if (keepId3) {
					// 保留ID3标签
					copyID3Tag(bis, output);
				} else {
					// 跳过ID3标签
					skipID3Tag(bis);
				}
			}

			// 复制音频数据
			copyAudioData(bis, output);
		}
	}

	/**
	 * 复制ID3标签
	 */
	private static void copyID3Tag(BufferedInputStream bis, BufferedOutputStream bos) throws IOException {
		byte[] header = new byte[10];
		bis.read(header);
		int tagSize = calculateId3TagSize(header);

		// 写入标签头
		bos.write(header);

		// 复制标签内容
		byte[] tagContent = new byte[tagSize];
		int bytesRead = bis.read(tagContent);
		if (bytesRead > 0) {
			bos.write(tagContent, 0, bytesRead);
		}
	}

	/**
	 * 计算ID3标签大小
	 */
	private static int calculateId3TagSize(byte[] header) {
		// ID3标签大小使用syncsafe整数格式
		return (header[6] & 0x7F) << 21 | (header[7] & 0x7F) << 14 | (header[8] & 0x7F) << 7 | (header[9] & 0x7F);
	}

	/**
	 * 复制音频数据
	 */
	private static void copyAudioData(BufferedInputStream bis, BufferedOutputStream bos) throws IOException {
		byte[] buffer = new byte[8192];
		int bytesRead;
		while ((bytesRead = bis.read(buffer)) != -1) {
			bos.write(buffer, 0, bytesRead);
		}
		bos.flush();
	}

	/**
	 * 安全合并（防止覆盖原始文件）
	 * 
	 * @param firstFilePath  第一个MP3文件路径
	 * @param secondFilePath 第二个MP3文件路径
	 * @param outputFilePath 输出文件路径
	 * @return 合并是否成功
	 */
	public static boolean safeMerge(String firstFilePath, String secondFilePath, String outputFilePath) {
		try {
			mergeMp3Files(firstFilePath, secondFilePath, outputFilePath);
			return true;
		} catch (IOException e) {
			System.err.println("合并失败: " + e.getMessage());
			return false;
		}
	}

	/**
	 * 合并并覆盖第一个文件
	 * 
	 * @param firstFilePath  第一个MP3文件路径（将被覆盖）
	 * @param secondFilePath 第二个MP3文件路径
	 * @return 合并是否成功
	 */
	public static boolean mergeAndOverwriteFirst(String firstFilePath, String secondFilePath) {
		// 创建临时文件
		String tempFilePath = firstFilePath + ".tmp";

		try {
			// 合并到临时文件
			mergeMp3Files(firstFilePath, secondFilePath, tempFilePath);

			// 删除原始文件
			File originalFile = new File(firstFilePath);
			if (originalFile.exists() && !originalFile.delete()) {
				throw new IOException("无法删除原始文件");
			}

			// 重命名临时文件
			File tempFile = new File(tempFilePath);
			if (!tempFile.renameTo(originalFile)) {
				throw new IOException("无法重命名临时文件");
			}

			return true;
		} catch (IOException e) {
			System.err.println("合并覆盖失败: " + e.getMessage());

			// 清理临时文件
			File tempFile = new File(tempFilePath);
			if (tempFile.exists()) {
				tempFile.delete();
			}

			return false;
		}
	}

	public static void main(String[] args) throws IOException {
		mergeAndOverwriteFirst("D:/test/sra/1/audio_s.mp3", "D:/test/sra/1/audio_0.mp3");
		mergeAndOverwriteFirst("D:/test/sra/1/audio_s.mp3", "D:/test/sra/1/audio_1.mp3");
		mergeAndOverwriteFirst("D:/test/sra/1/audio_s.mp3", "D:/test/sra/1/audio_0.mp3");
//		List<String> lst = Arrays.asList("D:/test/sra/1/audio_0.mp3", "D:/test/sra/1/audio_1.mp3");
//		mergeMp3Files(lst, "D:/test/sra/1/audio_s.mp3");
	}
}
