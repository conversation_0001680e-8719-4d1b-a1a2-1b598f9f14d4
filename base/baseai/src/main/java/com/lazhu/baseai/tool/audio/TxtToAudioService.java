package com.lazhu.baseai.tool.audio;

import java.io.File;
import java.nio.charset.Charset;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.ForkJoinTask;
import java.util.concurrent.TimeUnit;

import com.lazhu.baseai.tool.audio.CosyVoiceTool.AudioInfo;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class TxtToAudioService {

	private static String MP3_TAR_PAT = "{0}/{1}/audio.mp3";

	private static String SRT_TAR_PAT = "{0}/{1}/audio.srt";

	private static String MP3_SUB_TAR_PAT = "{0}/{1}/audio_{2}.mp3";

	private static String FOLDER_PAT = "{0}/{1}";

	private static String SRT_ITEM = """
			{0}
			{1} --> {2}
			{3}
						""";

	public static boolean exec(String id, String folder, String txt, String voiceId) {
//		String[] str = TxtSplitter.splitIntoSentences(txt);
		String[] str = {txt};
		ForkJoinPool pool = ForkJoinPool.commonPool();
		List<ForkJoinTask<AudioInfo>> lst = new ArrayList<>(str.length);
		String fd = MessageFormat.format(FOLDER_PAT, folder, id);
		FileUtil.del(fd);
		FileUtil.mkdirsSafely(new File(fd), 1, 100);
		long start = System.currentTimeMillis();
		for (int i = 0; i < str.length; i++) {
			final int n = i;
			if (StrUtil.isNotBlank(str[n]) && str[n].length() > 1) {
				// 阿里云要求语音合成每秒请求数不能超过3次，这里限制频率
                try {
                    TimeUnit.MILLISECONDS.sleep(500);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                ForkJoinTask<AudioInfo> fk = pool.submit(() -> {
					AudioInfo audio = CosyVoiceTool.createAudio(n, voiceId, str[n],
							MessageFormat.format(MP3_SUB_TAR_PAT, folder, id, n));
					return audio;
				});
				lst.add(fk);
			}
		}
		for (ForkJoinTask<AudioInfo> f : lst) {
			f.join();
		}
		log.info("语言合成完成耗时：{} s", (System.currentTimeMillis() - start)/1000);
		List<AudioInfo> as = new ArrayList<CosyVoiceTool.AudioInfo>(lst.size());
		for (ForkJoinTask<AudioInfo> f : lst) {
			try {
				as.add(f.get());
			} catch (InterruptedException e) {
				e.printStackTrace();
			} catch (ExecutionException e) {
				e.printStackTrace();
			}
		}
		String mp3Tar = MessageFormat.format(MP3_TAR_PAT, folder, id);
		String srtTar = MessageFormat.format(SRT_TAR_PAT, folder, id);
		Long c = 0L;
		as = as.stream().sorted((a, b) -> a.seq() > b.seq() ? 1 : 0).toList();
		long start2 = System.currentTimeMillis();
		for (int i = 0; i < as.size(); i++) {
			String subFile = MessageFormat.format(MP3_SUB_TAR_PAT, folder, id, as.get(i).seq());
			MP3Tool.mergeAndOverwriteFirst(mp3Tar, subFile);
			FileUtil.appendLines(
					Arrays.asList(MessageFormat.format(SRT_ITEM, i + 1, TimeFormatUtil.formatTime(c),
							TimeFormatUtil.formatTime(c += as.get(i).timeLong()), as.get(i).txt())),
					srtTar, Charset.forName("utf-8"));
			FileUtil.del(subFile);
		}
		log.info("字幕文件生成完成，耗时：{}s", (System.currentTimeMillis() - start2)/1000);
		return true;
	}

	public static void main(String[] args) {
		exec("1", "d:/test/sra", "这两年，继盲盒火爆后，盲卡凭借“未知惊喜”的卖点，迅速在儿童青少年群体中走红。非理性消费在儿童青少年中屡见不鲜，部分孩子为了追求稀有卡牌，单次消费高达数百元甚至数千元。",
				"longxiaochun_v2");
	}

}
