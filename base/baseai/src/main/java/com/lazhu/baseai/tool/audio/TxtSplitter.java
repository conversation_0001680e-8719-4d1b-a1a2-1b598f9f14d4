package com.lazhu.baseai.tool.audio;

import java.util.ArrayList;
import java.util.List;

public class TxtSplitter {

	/**
	 * 将字符串按标点符号拆分成数组（保留标点符号）
	 * 
	 * @param input 输入字符串
	 * @return 拆分后的字符串数组
	 */
	public static String[] splitByPunctuation(String input) {
		if (input == null || input.isEmpty()) {
			return new String[0];
		}

		// 正则表达式：匹配标点符号或非标点序列
		// \\p{Punct} 匹配任何英文标点：!"#$%&'()*+,-./:;<=>?@[\]^_`{|}~
		// \\p{IsPunctuation} 匹配任何Unicode标点（包括中文标点）
		String regex = "(?<=\\p{Punct}|\\p{IsPunctuation})|(?=\\p{Punct}|\\p{IsPunctuation})";

		// 处理连续标点符号的情况
		List<String> result = new ArrayList<>();
		String[] temp = input.split(regex);

		for (String s : temp) {
			if (!s.trim().isEmpty()) {
				result.add(s);
			}
		}

		return result.toArray(new String[0]);
	}

	/**
	 * 将字符串按标点符号拆分成数组（可选择是否保留标点）
	 * 
	 * @param input           输入字符串
	 * @param keepPunctuation 是否保留标点符号
	 * @return 拆分后的字符串数组
	 */
	public static String[] splitByPunctuation(String input, boolean keepPunctuation) {
		if (!keepPunctuation) {
			// 不保留标点：使用标点作为分隔符
			return input.split("[\\p{Punct}\\p{IsPunctuation}]+");
		}
		return splitByPunctuation(input);
	}

	/**
	 * 将字符串按句子拆分（以句末标点分割）
	 * 
	 * @param input 输入字符串
	 * @return 句子数组
	 */
	public static String[] splitIntoSentences(String input) {
		if (input == null || input.isEmpty()) {
			return new String[0];
		}

		// 匹配句末标点：.?!。？！等
		String regex = "(?<=[.!?。？！])";
		String[] sentences = input.split(regex);

		// 移除空句子
		List<String> result = new ArrayList<>();
		for (String s : sentences) {
			if (!s.trim().isEmpty()) {
				result.add(s.trim());
			}
		}

		return result.toArray(new String[0]);
	}

	// 测试
	public static void main(String[] args) {
		String text = "你好，世界！这是一个示例。测试多种标点：如！、？；... 结束";

		System.out.println("包含标点的拆分:");
		for (String s : splitByPunctuation(text)) {
			System.out.println("[" + s + "]");
		}

		System.out.println("\n不包含标点的拆分:");
		for (String s : splitByPunctuation(text, false)) {
			System.out.println("[" + s + "]");
		}

		System.out.println("\n按句子拆分:");
		for (String s : splitIntoSentences(text)) {
			System.out.println("[" + s + "]");
		}
	}
}