package com.lazhu.baseai.tool.audio;

public class TimeFormatUtil {

	/**
	 * 将毫秒数转换为时间码格式 (HH:mm:ss,SSS)
	 * 
	 * @param milliseconds 毫秒数
	 * @return 格式化后的时间字符串
	 */
	public static String formatTime(long milliseconds) {
		// 确保输入值非负
		if (milliseconds < 0) {
			throw new IllegalArgumentException("输入时间不能为负数");
		}

		// 计算各部分时间值
		long hours = milliseconds / (3600 * 1000);
		long remaining = milliseconds % (3600 * 1000);

		long minutes = remaining / (60 * 1000);
		remaining = remaining % (60 * 1000);

		long seconds = remaining / 1000;
		long millis = remaining % 1000;

		// 格式化各部分为两位数
		return String.format("%02d:%02d:%02d,%03d", hours, minutes, seconds, millis);
	}

	/**
	 * 将时间码格式 (HH:mm:ss,SSS) 转换回毫秒数
	 * 
	 * @param timeString 时间字符串
	 * @return 对应的毫秒数
	 */
	public static long parseTime(String timeString) {
		if (timeString == null || !timeString.matches("\\d{2}:\\d{2}:\\d{2},\\d{3}")) {
			throw new IllegalArgumentException("无效的时间格式，应为 HH:mm:ss,SSS");
		}

		String[] parts = timeString.split("[:,]");
		long hours = Long.parseLong(parts[0]);
		long minutes = Long.parseLong(parts[1]);
		long seconds = Long.parseLong(parts[2]);
		long millis = Long.parseLong(parts[3]);

		return (hours * 3600 * 1000) + (minutes * 60 * 1000) + (seconds * 1000) + millis;
	}

	// 测试方法
	public static void main(String[] args) {
		// 测试毫秒转时间码
		long[] testTimes = { 0, 1000, 38120, 3600000, 45296230 };
		for (long time : testTimes) {
			System.out.printf("%d ms → %s%n", time, formatTime(time));
		}

		// 测试时间码转毫秒
		String[] testStrings = { "00:00:00,000", "00:00:01,000", "00:00:38,120", "01:00:00,000", "12:34:56,230" };
		for (String timeStr : testStrings) {
			System.out.printf("%s → %d ms%n", timeStr, parseTime(timeStr));
		}
	}
}