package com.lazhu.baseai.llm.provider;

/**
 * 语音合成大模型提供者接口
 */
public interface VoiceLLMProvider {
    /**
     * 获取提供者名称
     */
    String getProviderName();

    /**
     * 创建声音模型
     * @param voiceName 声音名称
     * @param voiceUrl 声音文件路径
     * @return 声音ID
     */
    String createVoice(String voiceName, String voiceUrl);

    /**
     * 文本转语音
     * @param text 文本内容
     * @param voiceId 声音ID
     * @return 音频文件路径
     */
    String textToSpeech(String id,String text, String voiceId);
}
