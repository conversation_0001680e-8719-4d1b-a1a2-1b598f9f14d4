package com.lazhu.baseai.tool.audio;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * TTS优化工具类
 * 用于调用TTS优化接口，将普通文本转换为带有SSML标记的优化文本
 */
@Slf4j
@Component
public class TTSOptimizationTool {

    /**
     * TTS优化接口URL
     */
    @Value("${ai.tts_optimization_url:}")
    private String optimizationUrl;

    /**
     * 认证token
     */
    @Value("${ai.tts_optimization_auth:}")
    private String authToken;


    private static final String request_body = """
            {
                "inputs": {
                    "text": ""
                },
                "response_mode": "blocking",
                "user": "user-123"
            }
            """;


    /**
     * 优化TTS文本
     *
     * @param text 原始文本
     * @return 优化后的TTS文本
     */
    public String optimizeText(String text) {
        String response = optimizeTextWithFullResponse(text);
        if (StrUtil.isNotEmpty(response)) {
            return response;
        }
        log.warn("TTS优化失败，返回原始文本");
        return text; // 如果优化失败，返回原始文本
    }

    /**
     * 优化TTS文本并返回完整响应
     *
     * @param text 原始文本
     * @return 完整的响应对象
     */
    public String optimizeTextWithFullResponse(String text) {
        if (StrUtil.isEmpty(text)) {
            log.warn("输入文本为空，跳过TTS优化");
            return null;
        }
        // 构建请求对象
        JSONObject object = JSONObject.parseObject(request_body);
        object.getJSONObject("inputs").put("text", text);
        // 转换为JSON
        String requestJson = object.toJSONString();
        log.info("TTS优化接口请求参数：{}", requestJson);

        // 创建HTTP请求
        HttpRequest httpRequest = HttpUtil.createPost(optimizationUrl)
                .header("Authorization", authToken)
                .header("Content-Type", "application/json")
                .body(requestJson);
        // 发送请求
        try (HttpResponse httpResponse = httpRequest.execute()) {
            String responseBody = httpResponse.body();
            log.info("TTS优化接口响应：{}", responseBody);

            if (!httpResponse.isOk()) {
                log.error("TTS优化接口请求失败，状态码：{}, 响应：{}", httpResponse.getStatus(), responseBody);
                return null;
            }

            // 解析响应
            JSONObject response = JSONObject.parseObject(responseBody);
            if (response == null) {
                log.error("TTS优化接口响应解析失败");
                return null;
            }
            JSONObject data = response.getJSONObject("data");

            if (data == null || !"succeeded".equals(data.getString("status"))) {
                log.warn("TTS优化任务执行失败,错误信息：{}", response);
                return null;
            }
            JSONObject outputs = data.getJSONObject("outputs");
            if (outputs == null) {
                log.warn("TTS优化任务执行失败,错误信息：{}", response);
                return null;
            }
            return outputs.getString("text");
        } catch (Exception ex) {
            log.error("TTS优化接口请求失败",ex);
            return null;
        }
    }


    /**
     * 测试方法
     */
    public static void main(String[] args) {
        TTSOptimizationTool tool = new TTSOptimizationTool();
        tool.optimizationUrl = "http://192.168.33.174/v1/workflows/run";
        tool.authToken = "Bearer app-zpaCirB7XVa9qmVdEY0nt814";

        String testText = "这两年，继盲盒火爆后，盲卡凭借“未知惊喜”的卖点，迅速在儿童青少年群体中走红。非理性消费在儿童青少年中屡见不鲜，部分孩子为了追求稀有卡牌，单次消费高达数百元甚至数千元。";
        String optimizedText = tool.optimizeText(testText);

        System.out.println("原始文本：" + testText);
        System.out.println("优化后文本：" + optimizedText);
    }
}
