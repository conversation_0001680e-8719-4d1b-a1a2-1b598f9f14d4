package com.lazhu.baseai.tool.audio;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.HashMap;
import java.util.Map;

public class Mp3DurationReader {

	// MPEG版本枚举
	private enum MpegVersion {
		MPEG1, MPEG2, MPEG25, UNKNOWN
	}

	// 层枚举
	private enum Layer {
		LAYER_I, LAYER_II, LAYER_III, UNKNOWN
	}

	// 采样率映射表 (Hz)
	private static final Map<Integer, Map<MpegVersion, Integer>> SAMPLE_RATES = new HashMap<>();
	static {
		Map<MpegVersion, Integer> sampleRates44 = new HashMap<>();
		sampleRates44.put(MpegVersion.MPEG1, 44100);
		sampleRates44.put(MpegVersion.MPEG2, 22050);
		sampleRates44.put(MpegVersion.MPEG25, 11025);
		SAMPLE_RATES.put(0, sampleRates44);

		Map<MpegVersion, Integer> sampleRates48 = new HashMap<>();
		sampleRates48.put(MpegVersion.MPEG1, 48000);
		sampleRates48.put(MpegVersion.MPEG2, 24000);
		sampleRates48.put(MpegVersion.MPEG25, 12000);
		SAMPLE_RATES.put(1, sampleRates48);

		Map<MpegVersion, Integer> sampleRates32 = new HashMap<>();
		sampleRates32.put(MpegVersion.MPEG1, 32000);
		sampleRates32.put(MpegVersion.MPEG2, 16000);
		sampleRates32.put(MpegVersion.MPEG25, 8000);
		SAMPLE_RATES.put(2, sampleRates32);
	}

	// 比特率映射表 (kbps)
	private static final Map<MpegVersion, Map<Layer, int[]>> BITRATES = new HashMap<>();
	static {
		// MPEG1
		Map<Layer, int[]> mpeg1Bitrates = new HashMap<>();
		mpeg1Bitrates.put(Layer.LAYER_I,
				new int[] { 0, 32, 64, 96, 128, 160, 192, 224, 256, 288, 320, 352, 384, 416, 448 });
		mpeg1Bitrates.put(Layer.LAYER_II,
				new int[] { 0, 32, 48, 56, 64, 80, 96, 112, 128, 160, 192, 224, 256, 320, 384 });
		mpeg1Bitrates.put(Layer.LAYER_III,
				new int[] { 0, 32, 40, 48, 56, 64, 80, 96, 112, 128, 160, 192, 224, 256, 320 });
		BITRATES.put(MpegVersion.MPEG1, mpeg1Bitrates);

		// MPEG2/2.5
		Map<Layer, int[]> mpeg2Bitrates = new HashMap<>();
		mpeg2Bitrates.put(Layer.LAYER_I,
				new int[] { 0, 32, 48, 56, 64, 80, 96, 112, 128, 144, 160, 176, 192, 224, 256 });
		mpeg2Bitrates.put(Layer.LAYER_II, new int[] { 0, 8, 16, 24, 32, 40, 48, 56, 64, 80, 96, 112, 128, 144, 160 });
		mpeg2Bitrates.put(Layer.LAYER_III, new int[] { 0, 8, 16, 24, 32, 40, 48, 56, 64, 80, 96, 112, 128, 144, 160 });
		BITRATES.put(MpegVersion.MPEG2, mpeg2Bitrates);
		BITRATES.put(MpegVersion.MPEG25, mpeg2Bitrates);
	}

	// 每帧样本数
	private static final Map<Layer, Map<MpegVersion, Integer>> SAMPLES_PER_FRAME = new HashMap<>();
	static {
		Map<MpegVersion, Integer> layer1Samples = new HashMap<>();
		layer1Samples.put(MpegVersion.MPEG1, 384);
		layer1Samples.put(MpegVersion.MPEG2, 384);
		layer1Samples.put(MpegVersion.MPEG25, 384);
		SAMPLES_PER_FRAME.put(Layer.LAYER_I, layer1Samples);

		Map<MpegVersion, Integer> layer2Samples = new HashMap<>();
		layer2Samples.put(MpegVersion.MPEG1, 1152);
		layer2Samples.put(MpegVersion.MPEG2, 1152);
		layer2Samples.put(MpegVersion.MPEG25, 1152);
		SAMPLES_PER_FRAME.put(Layer.LAYER_II, layer2Samples);

		Map<MpegVersion, Integer> layer3Samples = new HashMap<>();
		layer3Samples.put(MpegVersion.MPEG1, 1152);
		layer3Samples.put(MpegVersion.MPEG2, 576);
		layer3Samples.put(MpegVersion.MPEG25, 576);
		SAMPLES_PER_FRAME.put(Layer.LAYER_III, layer3Samples);
	}

	/**
	 * 获取MP3文件时长（秒）
	 * 
	 * @param filePath MP3文件路径
	 * @return 时长（秒），解析失败返回-1
	 */
	public static double getDuration(String filePath) {
		return getDuration(new File(filePath));
	}

	/**
	 * 获取MP3文件时长（毫秒）
	 * 
	 * @param filePath MP3文件路径
	 * @return 时长（秒），解析失败返回-1
	 */
	public static long getMillDuration(String filePath) {
		double duration = getDuration(new File(filePath));
		if (duration > 0d) {
			return Double.valueOf(duration * 1000).longValue();
		}
		return -1L;
	}

	/**
	 * 获取MP3文件时长（毫秒）
	 * 
	 * @param filePath MP3文件路径
	 * @return 时长（秒），解析失败返回-1
	 */
	public static long getMillDuration(File file) {
		double duration = getDuration(file);
		if (duration > 0d) {
			return Double.valueOf(duration * 1000).longValue();
		}
		return -1L;
	}

	/**
	 * 获取MP3文件时长（秒）
	 * 
	 * @param mp3File MP3文件对象
	 * @return 时长（秒），解析失败返回-1
	 */
	public static double getDuration(File mp3File) {
		try (RandomAccessFile raf = new RandomAccessFile(mp3File, "r")) {
			long fileSize = raf.length();
			long position = 0;

			// 跳过ID3标签
			position = skipID3v2Tag(raf);

			// 解析第一帧获取基本信息
			FrameInfo firstFrame = parseFrameHeader(raf, position);
			if (firstFrame == null) {
				return -1L;
			}

			// 尝试查找VBR头
			VbrInfo vbrInfo = findVbrHeader(raf, position, firstFrame.frameSize);
			if (vbrInfo != null) {
				return calculateDurationFromVbr(vbrInfo, firstFrame);
			}

			// 标准CBR文件
			return calculateDurationFromCbr(raf, position, fileSize, firstFrame);
		} catch (IOException e) {
			System.err.println("读取文件错误: " + e.getMessage());
			return -1;
		}
	}

	/**
	 * 跳过ID3v2标签
	 */
	private static long skipID3v2Tag(RandomAccessFile raf) throws IOException {
		raf.seek(0);
		byte[] header = new byte[10];
		if (raf.read(header) == 10) {
			// 检查ID3标识 (0x49 0x44 0x33)
			if (header[0] == 'I' && header[1] == 'D' && header[2] == '3') {
				int tagSize = (header[6] & 0x7F) << 21 | (header[7] & 0x7F) << 14 | (header[8] & 0x7F) << 7
						| (header[9] & 0x7F);
				return tagSize + 10; // 标签大小 + 头部大小
			}
		}
		return 0; // 没有ID3标签
	}

	/**
	 * 解析MP3帧头
	 */
	private static FrameInfo parseFrameHeader(RandomAccessFile raf, long position) throws IOException {
		raf.seek(position);
		byte[] headerBytes = new byte[4];
		if (raf.read(headerBytes) != 4) {
			return null;
		}

		int header = ((headerBytes[0] & 0xFF) << 24) | ((headerBytes[1] & 0xFF) << 16) | ((headerBytes[2] & 0xFF) << 8)
				| (headerBytes[3] & 0xFF);

		// 检查同步字 (11位)
		if ((header & 0xFFE00000) != 0xFFE00000) {
			return null;
		}

		// 解析MPEG版本
		MpegVersion version;
		switch ((header >> 19) & 0x3) {
		case 0:
			version = MpegVersion.MPEG25;
			break;
		case 2:
			version = MpegVersion.MPEG2;
			break;
		case 3:
			version = MpegVersion.MPEG1;
			break;
		default:
			return null; // 无效值
		}

		// 解析层
		Layer layer;
		switch ((header >> 17) & 0x3) {
		case 1:
			layer = Layer.LAYER_III;
			break;
		case 2:
			layer = Layer.LAYER_II;
			break;
		case 3:
			layer = Layer.LAYER_I;
			break;
		default:
			return null; // 无效值
		}

		// 获取比特率索引
		int bitrateIndex = (header >> 12) & 0xF;
		if (bitrateIndex == 0 || bitrateIndex == 15) {
			return null; // 无效比特率
		}

		// 获取采样率索引
		int sampleRateIndex = (header >> 10) & 0x3;
		if (sampleRateIndex == 3) {
			return null; // 无效采样率
		}

		// 获取填充位
		int padding = (header >> 9) & 0x1;

		// 计算帧大小
		int bitrate = BITRATES.get(version).get(layer)[bitrateIndex] * 1000; // bps
		int sampleRate = SAMPLE_RATES.get(sampleRateIndex).get(version);
		int samplesPerFrame = SAMPLES_PER_FRAME.get(layer).get(version);

		int frameSize;
		if (layer == Layer.LAYER_I) {
			frameSize = (12 * bitrate / sampleRate + padding) * 4;
		} else {
			frameSize = (samplesPerFrame * bitrate / (8 * sampleRate)) + padding;
		}

		return new FrameInfo(version, layer, bitrate, sampleRate, samplesPerFrame, frameSize);
	}

	/**
	 * 查找VBR头
	 */
	private static VbrInfo findVbrHeader(RandomAccessFile raf, long position, int frameSize) throws IOException {
		// 跳转到可能包含VBR头的位置
		raf.seek(position + 4 + 28); // 帧头后 + 28字节

		byte[] xingHeader = new byte[4];
		if (raf.read(xingHeader) != 4) {
			return null;
		}

		String xing = new String(xingHeader);
		if (!"Xing".equals(xing) && !"Info".equals(xing)) {
			return null;
		}

		// 读取标志位
		int flags = raf.readInt();

		// 检查是否包含帧数
		if ((flags & 0x01) == 0) {
			return null;
		}

		// 读取总帧数
		int totalFrames = raf.readInt();
		return new VbrInfo(totalFrames);
	}

	/**
	 * 从VBR信息计算时长
	 */
	private static double calculateDurationFromVbr(VbrInfo vbrInfo, FrameInfo frameInfo) {
		return (double) (vbrInfo.totalFrames * frameInfo.samplesPerFrame) / frameInfo.sampleRate;
	}

	/**
	 * 从CBR文件计算时长
	 */
	private static double calculateDurationFromCbr(RandomAccessFile raf, long start, long fileSize, FrameInfo frameInfo)
			throws IOException {
		// 计算音频数据大小（排除ID3v1标签）
		long audioDataSize = fileSize - start;
		if (fileSize >= 128) {
			raf.seek(fileSize - 128);
			byte[] tag = new byte[3];
			if (raf.read(tag) == 3 && tag[0] == 'T' && tag[1] == 'A' && tag[2] == 'G') {
				audioDataSize -= 128; // ID3v1标签大小
			}
		}

		// 计算帧数
		int frameCount = (int) (audioDataSize / frameInfo.frameSize);
		return (double) (frameCount * frameInfo.samplesPerFrame) / frameInfo.sampleRate;
	}

	// 帧信息容器类
	private static class FrameInfo {
		final MpegVersion version;
		final Layer layer;
		final int bitrate; // bps
		final int sampleRate; // Hz
		final int samplesPerFrame;
		final int frameSize; // bytes

		FrameInfo(MpegVersion version, Layer layer, int bitrate, int sampleRate, int samplesPerFrame, int frameSize) {
			this.version = version;
			this.layer = layer;
			this.bitrate = bitrate;
			this.sampleRate = sampleRate;
			this.samplesPerFrame = samplesPerFrame;
			this.frameSize = frameSize;
		}
	}

	// VBR信息容器类
	private static class VbrInfo {
		final int totalFrames;

		VbrInfo(int totalFrames) {
			this.totalFrames = totalFrames;
		}
	}

	// 测试方法
	public static void main(String[] args) {
		String filePath = "D:/test/sra/1/audio_s.mp3";
		double duration = getDuration(filePath);

		if (duration >= 0) {
			int totalSeconds = (int) duration;
			int hours = totalSeconds / 3600;
			int minutes = (totalSeconds % 3600) / 60;
			int seconds = totalSeconds % 60;
			int milliseconds = (int) ((duration - totalSeconds) * 1000);

			System.out.printf("MP3时长: %02d:%02d:%02d.%03d (%.3f秒)%n", hours, minutes, seconds, milliseconds, duration);
		} else {
			System.out.println("无法解析MP3时长");
		}
	}
}