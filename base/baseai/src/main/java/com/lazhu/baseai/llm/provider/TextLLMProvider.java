package com.lazhu.baseai.llm.provider;

import com.lazhu.baseai.llm.dto.LLMBaseRequest;
import com.lazhu.baseai.llm.dto.LLMBaseResponse;
import com.lazhu.baseai.llm.dto.TextCreationReq;
import com.lazhu.baseai.llm.dto.TextCreationResult;

import java.util.List;

/**
 * 文本生成大模型提供者接口
 */
public interface TextLLMProvider {
    /**
     * 获取提供者名称
     */
    String getProviderName();

    /**
     * 生成文本内容
     * @param request 生成文本请求参数
     * @return 生成的文本内容
     */
    LLMBaseResponse<List<TextCreationResult>> generateText(LLMBaseRequest<TextCreationReq> request);

    LLMBaseResponse<List<TextCreationResult>> finetuneTxt(LLMBaseRequest<TextCreationReq> req);
}
