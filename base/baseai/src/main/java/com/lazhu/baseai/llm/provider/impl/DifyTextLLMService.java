package com.lazhu.baseai.llm.provider.impl;

import com.lazhu.baseai.llm.dto.LLMBaseRequest;
import com.lazhu.baseai.llm.dto.LLMBaseResponse;
import com.lazhu.baseai.llm.dto.TextCreationReq;
import com.lazhu.baseai.llm.dto.TextCreationResult;
import com.lazhu.baseai.llm.provider.TextLLMProvider;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Function;

/**
 * dify 大模型文本生成服务调用
 */
@Slf4j
@Service
public class DifyTextLLMService implements TextLLMProvider {

    @Autowired
    private DifyVideoScriptService videoScriptService;

    @Autowired
    private DifyTextService textService;


    @Override
    public String getProviderName() {
        return "dify";
    }

    @Override
    public LLMBaseResponse<List<TextCreationResult>> generateText(LLMBaseRequest<TextCreationReq> request) {
        if (request.getParams().getContentType().equals("1")) {
            return textService.generateText(request);
        } else {
            return videoScriptService.generateText(request);
        }
    }

    @Override
    public LLMBaseResponse<List<TextCreationResult>> finetuneTxt(LLMBaseRequest<TextCreationReq> request) {
        if (request.getParams().getContentType().equals("1")) {
            return textService.finetuneTxt(request);
        } else {
            return videoScriptService.finetuneTxt(request);
        }
    }


    protected <T> T doRequest(String url, String token, JSONObject requestBody, Function<JSONObject, T> successHandler) {
        HttpRequest post = HttpUtil.createPost(url);
        //设置请求头
        post.header("Authorization", token);
        //设置请求体
        post.body(requestBody.toJSONString());

        long start = System.currentTimeMillis();
        log.info("dify接口请求参数：url:{},token:{},body:{}",url,token,requestBody.toJSONString());

        //发起请求
        JSONObject response;
        try (HttpResponse rehttpResponse = post.execute()) {
            String body = rehttpResponse.body();
            log.info("dify接口请求响应：{}，耗时：{}s", body, (System.currentTimeMillis() - start) / 1000);
            response = JSONObject.parseObject(body);
            return successHandler.apply(response);
        } catch (Exception e) {
            log.error("dify接口请求异常：{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

}
