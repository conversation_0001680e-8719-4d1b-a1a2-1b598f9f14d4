package com.lazhu.baseai.llm.provider;


import com.lazhu.baseai.llm.dto.*;

/**
 * 视频合成大模型提供者接口
 */
public interface VideoLLMProvider {
    /**
     * 获取提供者名称
     */
    String getProviderName();

    /**
     * 对口型视频合成
     * @return taskId
     */
    LLMBaseResponse<VideoSynthesizeTask> synthesizeVideo(LLMBaseRequest<VideoSynthesizeReq> request);

    /**
     * 查询视频合成结果
     * @param task 任务
     * @return 合成结果
     */
    LLMBaseResponse<VideoSynthesizeResp> querySynthesizeResult(LLMBaseRequest<VideoSynthesizeTask> task);
}
