package com.lazhu.baseai.llm.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 对口型视频合成响应
 */
@Data
public class VideoSynthesizeResp implements Serializable {

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 视频地址
     */
    private String videoUrl;

    /**
     * 视频时长
     */
    private Double videoDuration;

    /**
     * 异常信息
     */
    private Throwable throwable;

    /**
     * 响应编码
     */
    private String code;

    /**
     * 响应信息
     */
    private String msg;

}
