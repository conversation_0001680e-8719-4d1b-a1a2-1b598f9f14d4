package com.lazhu.baseai.llm.provider.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lazhu.baseai.llm.dto.LLMBaseRequest;
import com.lazhu.baseai.llm.dto.LLMBaseResponse;
import com.lazhu.baseai.llm.dto.TextCreationReq;
import com.lazhu.baseai.llm.dto.TextCreationResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * dify 大模型文本生成服务调用
 */
@Slf4j
@Service
public class DifyVideoScriptService extends DifyTextLLMService {

    @Value("${ai.url}")
    private String url;

    @Value("${ai.video_text_create_token}")
    private String token;


    /**
     * 视频口播文案请求体
     */
    private static final String REQUEST_BODY = """
            
            {
                "inputs": {
                    "prompt":"",
                    "edit_index": "",
                    "idea":""
                },
                "query": "",
                "files": [],
                "response_mode": "blocking",
                "conversation_id": "{}",
                "user": "lz_aigc"
            }
            
            """;

    @Override
    public LLMBaseResponse<List<TextCreationResult>> generateText(LLMBaseRequest<TextCreationReq> request) {
        log.info("开始处理dify视频口播文案生成");
        return doRequest(url, token, buildRequestBody(request), response -> {
            // 解析返回结果
            LLMBaseResponse<List<TextCreationResult>> llmResponse = new LLMBaseResponse<>();
            llmResponse.setConversationId(response.getString("conversation_id"));
            String answer = response.getString("answer");
            if (StrUtil.isBlank(answer)) {
                throw new RuntimeException(response.toJSONString());
            }
            List<TextCreationResult> list = new ArrayList<>();
            if (JSONUtil.isTypeJSONArray(answer)) {
                list = JSONArray.parseArray(answer, TextCreationResult.class);
            } else if (JSONUtil.isTypeJSON(answer)) {
                list.add(JSONObject.parseObject(answer, TextCreationResult.class));
            }
            llmResponse.setBody(list);
            return llmResponse;
        });
    }

    @Override
    public LLMBaseResponse<List<TextCreationResult>> finetuneTxt(LLMBaseRequest<TextCreationReq> req) {
        log.info("开始处理dify视频口播文案修改");
        return generateText(req);
    }


    private JSONObject buildRequestBody(LLMBaseRequest<TextCreationReq> request) {
        TextCreationReq params = request.getParams();
        JSONObject body = JSONObject.parseObject(REQUEST_BODY);

        JSONObject inputs = body.getJSONObject("inputs");
        inputs.put("prompt", params.getPrompt());
        inputs.put("edit_index", params.getEditIndex());
        inputs.put("idea", params.getIdea());

        body.put("conversation_id", request.getConversationId());

        body.put("query", params.getQuery());
        return body;
    }

    @Override
    public String getProviderName() {
        return "dify-video";
    }
}
