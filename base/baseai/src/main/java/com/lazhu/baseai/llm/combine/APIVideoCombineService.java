package com.lazhu.baseai.llm.combine;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 调用接口合成视频
 */
@Slf4j
@Service
public class APIVideoCombineService implements VideoCombineService {

    /**
     * 视频合成服务接口地址
     */
    private final String url = "http://192.168.33.174:8082/vd/video_cut";

    /**
     * 视频合成
     *
     * @param req 视频合成请求参数
     * @return 合成结果
     */
    @Override
    public VideoCombineResult combine(VideoCombineReq req) {
        log.info("===> 调用接口合成视频");

        JSONObject body = new JSONObject();
        body.put("video_urls", req.getVideoUrl());
        String srtPath = req.getSrtPath();
        //读取srt文件
        List<String> srtList = FileUtil.readLines(srtPath, "UTF-8");
        body.put("srt_text", CollUtil.join(srtList, "\n"));
        body.put("title_text", req.getTitleText());
        body.put("task_id", req.getTaskId());
        body.put("oss_path", req.getTaskId());
        log.info("===> 调用接口合成视频请求参数：{}", body.toJSONString());

        HttpRequest request = HttpUtil.createPost(url)
                .header("Content-Type", "application/json")
                .body(body.toJSONString());
        try (HttpResponse response = request.execute()) {
            String responseBody = response.body();
            log.info("===> 调用接口合成视频返回结果：{}", responseBody);
            JSONObject object = JSONObject.parseObject(responseBody);
            if (object.getInteger("code") != 200) {
                throw new RuntimeException("合成视频接口调用失败:" + responseBody);
            }
            //异步查询结果
            VideoCombineResult result = new VideoCombineResult();
            result.setTaskId(req.getTaskId());
            result.setDone(false);
            return result;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
