package com.lazhu.baseai.llm.factory;

import com.lazhu.baseai.llm.provider.TextLLMProvider;
import com.lazhu.baseai.llm.provider.VideoLLMProvider;
import com.lazhu.baseai.llm.provider.VoiceLLMProvider;
import cn.hutool.extra.spring.SpringUtil;
import com.lazhu.common.exception.BusinessException;
import org.springframework.stereotype.Component;

/**
 * 大模型提供者工厂
 */
@Component
public class LLMProviderFactory {


    /**
     * 获取文本生成模型提供者
     *
     * @return 文本生成模型提供者
     */
    public TextLLMProvider getTextProvider(String providerName) {
        return SpringUtil.getBeansOfType(TextLLMProvider.class).values().stream()
                .filter(provider -> provider.getProviderName().equals(providerName))
                .findFirst()
                .orElseThrow(() -> new BusinessException("未找到文本生成模型提供者: " + providerName));
    }

    /**
     * 获取语音合成模型提供者
     *
     * @return 语音合成模型提供者
     */
    public VoiceLLMProvider getVoiceProvider(String providerName) {
        return SpringUtil.getBeansOfType(VoiceLLMProvider.class).values().stream()
                .filter(provider -> provider.getProviderName().equals(providerName))
                .findFirst()
                .orElseThrow(() -> new BusinessException("未找到语音合成模型提供者: " + providerName));
    }

    /**
     * 获取视频合成模型提供者
     *
     * @return 视频合成模型提供者
     */
    public VideoLLMProvider getVideoProvider(String providerName) {
        return SpringUtil.getBeansOfType(VideoLLMProvider.class).values().stream()
                .filter(provider -> provider.getProviderName().equals(providerName))
                .findFirst()
                .orElseThrow(() -> new BusinessException("未找到视频合成模型提供者: " + providerName));
    }
}
