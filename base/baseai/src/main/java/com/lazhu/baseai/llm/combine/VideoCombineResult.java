package com.lazhu.baseai.llm.combine;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * 视频合成结果
 */
@Data
public class VideoCombineResult implements Serializable {


    /**
     * 视频路径
     */
    private  String videoPath;

    /**
     * 视频时长(秒)
     */
    @JSONField(serializeUsing = ToStringSerializer.class)
    private  Long duration;


    /**
     * 任务id
     */
    private  String taskId;

    /**
     * 当前步骤
     * 步骤枚举:
     * 语音合成：AC (audio_combine)
     * 对口型视频生成：VC (video_combine)
     * 字幕合成：SC (srt_combine)
     */
    private Step curStep;

    /**
     * 封面图
     */
    private String coverImage;

    /**
     * 是否完成
     */
    private boolean isDone;


    /**
     * 合成结果  0 失败  1成功
     */
    private String result;

    /**
     * 错误信息
     */
    private String errorMsg;


    public static enum Step{
        /**
         * 语音合成
         */
        AC,
        /**
         * 对口型视频生成
         */
        VC,
        /**
         * 字幕合成
         */
        SC
    }

    public VideoCombineResult(String videoPath, Long duration, String taskId, String coverImage, boolean isDone, String result, String errorMsg) {
        this.videoPath = videoPath;
        this.duration = duration;
        this.taskId = taskId;
        this.coverImage = coverImage;
        this.isDone = isDone;
        this.result = result;
        this.errorMsg = errorMsg;
    }

    public VideoCombineResult(){
    }
}
