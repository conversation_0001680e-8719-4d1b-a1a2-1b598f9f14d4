package com.lazhu.baseai.llm.provider.impl;

import com.alibaba.fastjson.JSONObject;
import com.lazhu.baseai.llm.dto.LLMBaseRequest;
import com.lazhu.baseai.llm.dto.LLMBaseResponse;
import com.lazhu.baseai.llm.dto.TextCreationReq;
import com.lazhu.baseai.llm.dto.TextCreationResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * dify 大模型文本生成服务调用
 */
@Slf4j
@Service
public class DifyTextService extends DifyTextLLMService {

    @Value("${ai.text_create_url}")
    private String textCreateUrl;

    @Value("${ai.text_create_token}")
    private String textCreateToken;

    @Value("${ai.url}")
    private String textModifyUrl;

    @Value("${ai.text_modify_token}")
    private String textModifyToken;


    /**
     * 视频口播文案请求体
     */
    private static final String CREATE_REQUEST_BODY = """
            
            {
            	"inputs": {
            		"idea": "",
            		"platform":"",
            		"prompt": ""
            	},
            	"response_mode": "blocking",
            	"user": "abc-123"
            }
            
            """;

    /**
     * 图文案请求体
     */
    private static final String MODIFY_REQUEST_BODY = """
            
            {
                "inputs": {
                    "title": "",
                    "content": ""
                },
                "query": "",
                "response_mode": "blocking",
                "conversation_id": "",
                "user": "abc-123"
            }
            
            """;

    @Override
    public LLMBaseResponse<List<TextCreationResult>> generateText(LLMBaseRequest<TextCreationReq> request) {
        log.info("开始处理dify图文文案生成");
        //设置请求体
        JSONObject requestBody = JSONObject.parseObject(CREATE_REQUEST_BODY);
        TextCreationReq params = request.getParams();
        JSONObject inputs = requestBody.getJSONObject("inputs");
        inputs.put("prompt", params.getPrompt());
        inputs.put("idea", params.getIdea());
        inputs.put("platform", params.getContentType());

        return doRequest(textCreateUrl, textCreateToken, requestBody, response -> {
            // 解析返回结果
            LLMBaseResponse<List<TextCreationResult>> llmResponse = new LLMBaseResponse<>();
            List<TextCreationResult> list = new ArrayList<>();
            JSONObject outputs = response.getJSONObject("data").getJSONObject("outputs");
            TextCreationResult textCreationResult = new TextCreationResult();
            textCreationResult.setTitle(outputs.getString("title"));
            textCreationResult.setDigest(outputs.getString("digest"));
            textCreationResult.setCopywriting(outputs.getString("content"));
            list.add(textCreationResult);
            llmResponse.setBody(list);
            return llmResponse;
        });
    }

    @Override
    public LLMBaseResponse<List<TextCreationResult>> finetuneTxt(LLMBaseRequest<TextCreationReq> request) {
        log.info("开始处理dify图文文案修改");
        //设置请求体
        JSONObject requestBody = JSONObject.parseObject(MODIFY_REQUEST_BODY);
        TextCreationReq params = request.getParams();
        JSONObject inputs = requestBody.getJSONObject("inputs");
        inputs.put("title", params.getTitle());
        inputs.put("content", params.getContent());
        requestBody.put("conversation_id", request.getConversationId());
        requestBody.put("query", params.getQuery());

        return doRequest(textModifyUrl, textModifyToken, requestBody, response -> {
            // 解析返回结果
            LLMBaseResponse<List<TextCreationResult>> llmResponse = new LLMBaseResponse<>();
            List<TextCreationResult> list = new ArrayList<>();
            JSONObject outputs = JSONObject.parseObject(response.getString("answer"));
            TextCreationResult textCreationResult = new TextCreationResult();
            textCreationResult.setTitle(outputs.getString("title"));
            textCreationResult.setDigest(outputs.getString("digest"));
            textCreationResult.setCopywriting(outputs.getString("content"));
            list.add(textCreationResult);
            llmResponse.setBody(list);
            llmResponse.setConversationId(response.getString("conversation_id"));
            return llmResponse;
        });
    }

    @Override
    public String getProviderName() {
        return "dify-text";
    }

}
