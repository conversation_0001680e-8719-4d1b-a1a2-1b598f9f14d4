package com.lazhu.baseai.llm.provider.impl;

import com.lazhu.baseai.llm.dto.*;
import com.lazhu.baseai.llm.provider.VideoLLMProvider;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.lazhu.common.utils.MediaUtil;
import com.lazhu.common.utils.OssUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 阿里云视频合成大模型提供者
 */
@Slf4j
@Service
public class AliVideoLLMProvider implements VideoLLMProvider {


    @Autowired
    private OssUtil ossUtil;

    /**
     * 最大视频音频时长（秒）
     */
    private static final int MAX_TASK_DURATION = 119;

    /**
     * 阿里云VideoRetalk API URL
     */
    private static final String SUBMIT_JOB_URL = "https://dashscope.aliyuncs.com/api/v1/services/aigc/image2video/video-synthesis/";
    private static final String QUERY_JOB_URL = "https://dashscope.aliyuncs.com/api/v1/tasks/";


    @Override
    public String getProviderName() {
        return "aliyun";
    }

    /**
     * 切分
     *
     * @param url 原始url
     * @return 切分后的url
     */
    private List<String> cut(String url) {
        long duration = MediaUtil.getDuration(url);
        List<String> result = new ArrayList<>();
        if (duration > MAX_TASK_DURATION) {
            // 截取份数
            int taskCount = (int) (duration / MAX_TASK_DURATION) + 1;
            long start = 0;
            long end = MAX_TASK_DURATION;
            for (int i = 0; i < taskCount; i++) {
                String filePath = MediaUtil.cut(url, start, end);
                //上传到oss
                File tmpFile = new File(filePath);
                String cutUrl = ossUtil.upload(tmpFile);
                log.info("视频截取上传oss成功：{}", cutUrl);
                boolean b = tmpFile.delete();
                result.add(cutUrl);
                start = end + 1;
                end = Math.min((start + MAX_TASK_DURATION), duration);
            }
        } else {
            result.add(url);
        }
        return result;
    }

    @Override
    public LLMBaseResponse<VideoSynthesizeTask> synthesizeVideo(LLMBaseRequest<VideoSynthesizeReq> request) {
        //任务列表
        List<String> taskIds = new ArrayList<>();
        VideoSynthesizeReq params = request.getParams();
        try {
            // 视频和音频切分
            List<String> audioList = cut(params.getAudioUrl());
            List<String> videoList = cut(params.getVideoUrl());
            // 以音频长度为基准合成视频
            int videoIndex = 0;
            for (int i = 0; i < audioList.size(); i++) {
                String audioUrl = audioList.get(i);
                String videoUrl = CollUtil.get(videoList, videoIndex);
                if (StrUtil.isBlank(videoUrl)) {
                    //视频长度小于音频长度，则重复视频片段
                    videoIndex = 0;
                    videoUrl = CollUtil.get(videoList, videoIndex);
                }
                String taskId = submitTask(videoUrl, audioUrl, request.getConfig());
                taskIds.add(taskId);
                videoIndex++;
            }
            LLMBaseResponse<VideoSynthesizeTask> response = new LLMBaseResponse<>();
            VideoSynthesizeTask videoSynthesizeResp = new VideoSynthesizeTask();
            videoSynthesizeResp.setTaskIds(taskIds);
            response.setBody(videoSynthesizeResp);
            return response;
        } catch (Exception e) {
            log.error("视频合成任务提交失败：{}", e.getMessage());
            throw new RuntimeException("视频合成任务提交失败：" + e.getMessage());
        }
    }

    /**
     * 提交单个任务
     *
     * @param videoUrl 视频URL
     * @param audioUrl 音频URL
     * @param config   大模型配置
     * @return 任务ID
     */
    private String submitTask(String videoUrl, String audioUrl, ModelConfig config) {
        // 构建请求体
        JSONObject requestBody = new JSONObject();
        requestBody.put("model", config.getModelId());

        JSONObject input = new JSONObject();
        input.put("video_url", videoUrl);
        input.put("audio_url", audioUrl);
        requestBody.put("input", input);

        JSONObject parameters = new JSONObject();
        parameters.put("video_extension", true);
        requestBody.put("parameters", parameters);

        // 创建HTTP请求
        HttpRequest post = HttpRequest.post(SUBMIT_JOB_URL)
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer " + config.getApiKey())
                .header("X-DashScope-Async", "enable").body(requestBody.toJSONString());

        // 发送请求并处理响应
        try (HttpResponse response = post.execute()) {
            String body = response.body();
            log.info("阿里云VideoRetalk API提交任务响应：{}", body);

            JSONObject jsonResponse = JSONObject.parseObject(body);
            if (jsonResponse.containsKey("output") && jsonResponse.getJSONObject("output").containsKey("task_id")) {
                return jsonResponse.getJSONObject("output").getString("task_id");
            } else {
                log.error("阿里云VideoRetalk API提交任务失败：{}", body);
                throw new RuntimeException("提交任务失败：" + body);
            }
        } catch (Exception e) {
            log.error("阿里云VideoRetalk API提交任务异常：", e);
            throw new RuntimeException("提交任务异常：" + e.getMessage());
        }
    }


    @Override
    public LLMBaseResponse<VideoSynthesizeResp> querySynthesizeResult(LLMBaseRequest<VideoSynthesizeTask> request) {

        // 获取API密钥
        String apiKey = request.getConfig().getApiKey();

        // 创建HTTP请求
        VideoSynthesizeTask params = request.getParams();
        String taskId = params.getTaskIds().get(0);
        HttpRequest get = HttpRequest.get(QUERY_JOB_URL + taskId).header("Authorization", "Bearer " + apiKey);

        // 发送请求并处理响应
        try (HttpResponse response = get.execute()) {
            String body = response.body();
            log.info("阿里云VideoRetalk API查询任务响应：{}", body);

            JSONObject jsonResponse = JSONObject.parseObject(body);
            VideoSynthesizeResp resp = new VideoSynthesizeResp();
            if (!jsonResponse.containsKey("output")) {
                throw new RuntimeException("查询任务失败：" + body);
            }
            JSONObject output = jsonResponse.getJSONObject("output");
            String taskStatus = output.getString("task_status");
            if ("SUCCEEDED".equals(taskStatus)) {
                // 如果任务成功，记录视频URL
                String videoUrl = output.getString("video_url");
                JSONObject usage = jsonResponse.getJSONObject("usage");
                resp.setVideoUrl(videoUrl);
                resp.setVideoDuration(usage.getDouble("video_duration"));
            }
            resp.setTaskId(taskId);
            resp.setStatus(taskStatus);
            resp.setCode(output.getString("code"));
            resp.setMsg(output.getString("message"));
            LLMBaseResponse<VideoSynthesizeResp> llmBaseResponse = new LLMBaseResponse<>();
            llmBaseResponse.setBody(resp);
            return llmBaseResponse;
        } catch (Exception e) {
            log.error("查询任务异常：", e);
            throw new RuntimeException("查询任务异常：" + e.getMessage());
        }

    }

}
